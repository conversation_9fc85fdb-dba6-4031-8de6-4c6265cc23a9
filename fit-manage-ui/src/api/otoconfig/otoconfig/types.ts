export interface OtoconfigVO {
  configId: number;
  /**
   * 参数名称
   */
  configName: string;

  /**
   * 参数键名
   */
  configKey: string;

  /**
   * 参数键值
   */
  configValue: string;

  /**
   * 系统内置（Y是 N否）
   */
  configType: string;

  /**
   * 创建时间
   */
  createTime: string;

  /**
   * 备注
   */
  remark: string;
}

export interface OtoconfigForm extends BaseEntity {
  /**
   * 参数ID
   */
  configId: number;

  /**
   * 参数名称
   */
  configName?: string;

  /**
   * 参数键名
   */
  configKey?: string;

  /**
   * 参数键值
   */
  configValue?: string;

  /**
   * 系统内置（Y是 N否）
   */
  configType?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface OtoconfigQuery extends PageQuery {
  /**
   * 参数名称
   */
  configName?: string;

  /**
   * 参数键名
   */
  configKey?: string;

  /**
   * 参数键值
   */
  configValue?: string;

  /**
   * 系统内置（Y是 N否）
   */
  configType?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
