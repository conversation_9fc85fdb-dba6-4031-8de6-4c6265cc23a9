/**
 * 用户注册日志（合规留痕/风控专用）查询参数
 */
export interface UserRegisterInfoQuery extends PageQuery {
  /**
   * 用户ID
   */
  userId?: string;

  /**
   * 注册手机号
   */
  encryptPhone?: string;

  /**
   * 手机号哈希
   */
  phoneHash?: string;

  /**
   * 注册IP地址
   */
  registerIp?: string;

  /**
   * 注册设备信息
   */
  registerDevice?: string;

  /**
   * 注册渠道
   */
  registerChannel?: string;

  /**
   * 邀请码
   */
  inviteCode?: string;

  /**
   * 注册状态：1-成功，2-失败，3-待验证
   */
  registerStatus?: number;

  /**
   * 加密初始向量
   */
  encryptIv?: string;

  /**
   * 国际电话区号
   */
  countryCode?: string;

  /**
   * 加密算法版本
   */
  encryptionVersion?: string;

  /**
   * 其他参数
   */
  params?: Record<string, any>;
}

/**
 * 用户注册日志（合规留痕/风控专用）对象
 */
export interface UserRegisterInfoVO extends BaseEntity {
  /**
   * 注册记录ID
   */
  id: string | number;

  /**
   * 用户ID
   */
  userId: string;

  /**
   * 注册手机号
   */
  encryptPhone: string;

  /**
   * 手机号哈希
   */
  phoneHash: string;

  /**
   * 注册IP地址
   */
  registerIp: string;

  /**
   * 注册设备信息
   */
  registerDevice: string;

  /**
   * 注册渠道
   */
  registerChannel: string;

  /**
   * 注册时短信验证码
   */
  smsCode: string;

  /**
   * 邀请码
   */
  inviteCode: string;

  /**
   * 注册状态：1-成功，2-失败，3-待验证
   */
  registerStatus: number;

  /**
   * 失败原因
   */
  failReason: string;

  /**
   * User-Agent信息
   */
  userAgent: string;

  /**
   * 注册地理位置
   */
  geoLocation: string;

  /**
   * 加密初始向量
   */
  encryptIv: string;

  /**
   * 国际电话区号
   */
  countryCode: string;

  /**
   * 加密算法版本
   */
  encryptionVersion: string;
}

/**
 * 用户注册日志（合规留痕/风控专用）表单对象
 */
export interface UserRegisterInfoForm {
  /**
   * 注册记录ID
   */
  id?: string | number;
}
