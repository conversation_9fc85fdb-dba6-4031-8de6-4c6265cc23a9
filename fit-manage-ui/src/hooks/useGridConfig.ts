import { ref, reactive, computed, nextTick } from 'vue';
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus';
import type { FormInstance, TableInstance } from 'element-plus';
import {
  listGridItem,
  getGridItem,
  delGridItem,
  addGridItem,
  updateGridItem,
  updateGridItemStatus,
  copyGridItem,
  listGridGroup
} from '@/api/otoconfig/gridconfig';
import type { GridItemQuery, GridItemVO, GridItemForm, GridGroupVO, TableDataInfo, BatchSortRequest } from '@/api/otoconfig/gridconfig/types';


/**
 * 金刚位配置管理组合式函数
 * 提供金刚位配置的完整功能，包括查询、增删改查、批量操作等
 */
export function useGridConfig() {
  // 响应式数据
  const loading = ref(false);
  const tableLoading = ref(false);
  const submitLoading = ref(false);
  const exportLoading = ref(false);

  // 表格数据
  const gridList = ref<GridItemVO[]>([]);
  const total = ref(0);
  const selectedIds = ref<number[]>([]);
  const selectedRows = ref<GridItemVO[]>([]);

  // 分组数据
  const groupList = ref<GridGroupVO[]>([]);
  const groupOptions = computed(() => [
    { label: '全部分组', value: '' },
    ...groupList.value.map((group) => ({
      label: group.groupName,
      value: group.id
    }))
  ]);

  // 查询参数
  const queryParams = reactive<GridItemQuery>({
    pageNum: 1,
    pageSize: 10,
    title: '',
    groupId: undefined,
    platform: ''
  });

  // 表单数据
  const formData = reactive<GridItemForm>({
    id: undefined,
    title: '',
    icon: '',
    route: '',
    groupId: undefined,
    platform: 'ALL',
    serviceType: 'INTERNAL',
    sortOrder: 0,
    isActive: '0',
    remark: ''
  });

  // 对话框状态
  const dialogVisible = ref(false);
  const dialogTitle = ref('');
  const isEdit = ref(false);

  // 表单引用
  const queryFormRef = ref<FormInstance>();
  const formRef = ref<FormInstance>();
  const tableRef = ref<TableInstance>();



  /**
   * 获取金刚位列表
   */
  const getGridList = async () => {
    try {
      tableLoading.value = true;

      const response = await listGridItem(queryParams);
      if (response.code === 200) {
        gridList.value = response.rows || [];
        total.value = response.total || 0;

        ElMessage.success(`加载成功，共 ${total.value} 条数据`);
      } else {
        ElMessage.error(response.msg || '获取数据失败');
      }
    } catch (error) {
      console.error('获取金刚位列表失败:', error);
      ElMessage.error('获取数据失败，请稍后重试');
    } finally {
      tableLoading.value = false;
    }
  };

  /**
   * 获取分组列表
   */
  const getGroupList = async () => {
    try {
      const response = await listGridGroup({ pageNum: 1, pageSize: 100 });
      if (response.code === 200) {
        groupList.value = response.data?.rows || [];
      }
    } catch (error) {
      console.error('获取分组列表失败:', error);
    }
  };

  /**
   * 搜索处理
   */
  const handleQuery = () => {
    queryParams.pageNum = 1;
    getGridList();
  };

  /**
   * 重置搜索
   */
  const resetQuery = () => {
    queryFormRef.value?.resetFields();
    Object.assign(queryParams, {
      pageNum: 1,
      pageSize: 10,
      title: '',
      groupId: undefined,
      platform: '',
      status: ''
    });
    getGridList();
  };

  /**
   * 分页变化
   */
  const handlePageChange = (page: number) => {
    queryParams.pageNum = page;
    getGridList();
  };

  /**
   * 页大小变化
   */
  const handleSizeChange = (size: number) => {
    queryParams.pageSize = size;
    queryParams.pageNum = 1;
    getGridList();
  };

  /**
   * 打开新增对话框
   */
  const handleAdd = () => {
    resetForm();
    dialogTitle.value = '添加金刚位';
    isEdit.value = false;
    dialogVisible.value = true;
  };

  /**
   * 打开编辑对话框
   */
  const handleEdit = (row: GridItemVO) => {
    resetForm();
    Object.assign(formData, {
      id: row.id,
      title: row.title,
      icon: row.icon,
      route: row.route,
      groupId: row.groupId,
      platform: row.platform,
      serviceType: row.serviceType,
      sortOrder: row.sortOrder,
      isActive: row.isActive,
      remark: row.remark
    });
    dialogTitle.value = '修改金刚位';
    isEdit.value = true;
    dialogVisible.value = true;
  };

  /**
   * 提交表单
   */
  const handleSubmit = async () => {
    if (!formRef.value) return;

    try {
      const valid = await formRef.value.validate();
      if (!valid) return;

      submitLoading.value = true;

      const apiCall = isEdit.value ? updateGridItem(formData) : addGridItem(formData);

      const response = await apiCall;

      if (response.code === 200) {
        ElMessage.success(isEdit.value ? '修改成功' : '添加成功');
        dialogVisible.value = false;
        // 刷新列表
        await getGridList();
      } else {
        ElMessage.error(response.msg || '操作失败');
      }
    } catch (error) {
      console.error('提交失败:', error);
      ElMessage.error('操作失败，请稍后重试');
    } finally {
      submitLoading.value = false;
    }
  };

  /**
   * 删除单个项目
   */
  const handleDelete = async (row: GridItemVO) => {
    try {
      await ElMessageBox.confirm(`确定要删除金刚位 "${row.title}" 吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      });

      const response = await delGridItem(row.id!);

      if (response.code === 200) {
        ElMessage.success('删除成功');
        // 刷新列表
        await getGridList();
      } else {
        ElMessage.error(response.msg || '删除失败');
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error);
        ElMessage.error('删除失败，请稍后重试');
      }
    }
  };

  /**
   * 切换状态
   */
  const handleStatusChange = async (row: GridItemVO) => {
    try {
      const response = await updateGridItemStatus(row.id!, row.isActive);

      if (response.code === 200) {
        ElMessage.success('状态更新成功');
      } else {
        // 恢复原状态
        row.isActive = row.isActive === '0' ? '1' : '0';
        ElMessage.error(response.msg || '状态更新失败');
      }
    } catch (error) {
      // 恢复原状态
      row.isActive = row.isActive === '0' ? '1' : '0';
      console.error('状态更新失败:', error);
      ElMessage.error('状态更新失败，请稍后重试');
    }
  };

  /**
   * 复制项目
   */
  const handleCopy = async (row: GridItemVO) => {
    try {
      const response = await copyGridItem({ sourceId: row.id! });

      if (response.code === 200) {
        ElMessage.success('复制成功');
        // 刷新列表
        await getGridList();
      } else {
        ElMessage.error(response.msg || '复制失败');
      }
    } catch (error) {
      console.error('复制失败:', error);
      ElMessage.error('复制失败，请稍后重试');
    }
  };

  /**
   * 导出数据
   */
  const handleExport = async () => {
    try {
      exportLoading.value = true;

      await ElMessageBox.confirm('确定要导出所有金刚位配置数据吗？', '导出确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      });

      // 导出功能暂未实现
      ElMessage.warning('导出功能暂未实现');
      ElMessage.success('导出成功');
    } catch (error) {
      if (error !== 'cancel') {
        console.error('导出失败:', error);
        ElMessage.error('导出失败，请稍后重试');
      }
    } finally {
      exportLoading.value = false;
    }
  };

  /**
   * 表格选择变化
   */
  const handleSelectionChange = (selection: GridItemVO[]) => {
    selectedRows.value = selection;
    selectedIds.value = selection.map((row) => Number(row.id)).filter((id) => !isNaN(id));
  };

  /**
   * 重置表单
   */
  const resetForm = () => {
    Object.assign(formData, {
      id: undefined,
      title: '',
      icon: '',
      route: '',
      groupId: undefined,
      platform: 'ALL',
      serviceType: 'INTERNAL',
      sortOrder: 0,
      isActive: '0',
      remark: ''
    });
    nextTick(() => {
      formRef.value?.clearValidate();
    });
  };





  // 表单验证规则
  const formRules = {
    title: [
      { required: true, message: '请输入标题', trigger: 'blur' },
      { min: 1, max: 50, message: '标题长度在 1 到 50 个字符', trigger: 'blur' }
    ],
    icon: [{ required: true, message: '请输入图标', trigger: 'blur' }],
    route: [{ required: true, message: '请输入路由地址', trigger: 'blur' }],
    groupId: [{ required: true, message: '请选择分组', trigger: 'change' }],
    platform: [{ required: true, message: '请选择平台', trigger: 'change' }],
    type: [{ required: true, message: '请选择类型', trigger: 'change' }],
    sortOrder: [
      { required: true, message: '请输入排序', trigger: 'blur' },
      { type: 'number', min: 0, max: 9999, message: '排序值在 0 到 9999 之间', trigger: 'blur' }
    ]
  };

  return {
    // 响应式数据
    loading,
    tableLoading,
    submitLoading,
    exportLoading,
    gridList,
    total,
    selectedIds,
    selectedRows,
    groupList,
    groupOptions,
    queryParams,
    formData,
    dialogVisible,
    dialogTitle,
    isEdit,
    formRules,

    // 引用
    queryFormRef,
    formRef,
    tableRef,

    // 方法
    getGridList,
    getGroupList,
    handleQuery,
    resetQuery,
    handlePageChange,
    handleSizeChange,
    handleAdd,
    handleEdit,
    handleSubmit,
    handleDelete,
    handleStatusChange,
    handleCopy,
    handleExport,
    handleSelectionChange,
    resetForm
  };
}
