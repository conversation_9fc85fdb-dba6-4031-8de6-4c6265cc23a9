<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="分组名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入分组名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="平台" prop="platform">
              <el-select v-model="queryParams.platform" placeholder="请选择平台" clearable>
                <el-option label="APP" value="APP" />
                <el-option label="H5" value="H5" />
                <el-option label="小程序" value="MINI_PROGRAM" />
              </el-select>
            </el-form-item>
            <el-form-item label="类型" prop="type">
              <el-select v-model="queryParams.type" placeholder="请选择类型" clearable>
                <el-option label="默认" value="DEFAULT" />
                <el-option label="自定义" value="CUSTOM" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
                <el-option label="启用" value="1" />
                <el-option label="禁用" value="0" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['otoconfig:gridGroup:add']"> 新增 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['otoconfig:gridGroup:edit']"
              >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['otoconfig:gridGroup:remove']"
              >删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['otoconfig:gridGroup:export']">导出 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="Setting" @click="handleSetDefault">设为默认</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table ref="tableRef" v-loading="loading" border :data="gridGroupList" @selection-change="handleSelectionChange" @row-click="handleRowClick">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="false" />
        <el-table-column label="分组名称" align="center" prop="name" />
        <el-table-column label="平台" align="center" prop="platform" />
        <el-table-column label="类型" align="center" prop="type">
          <template #default="scope">
            <el-tag v-if="scope.row.type === 'DEFAULT'" type="success">默认</el-tag>
            <el-tag v-else type="info">自定义</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="最大项目数" align="center" prop="maxItems" width="100" />
        <el-table-column label="排序" align="center" prop="sortOrder" width="80" />
        <el-table-column label="状态" align="center" prop="status" width="80">
          <template #default="scope">
            <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" @change="handleStatusChange(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['otoconfig:gridGroup:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="复制" placement="top">
              <el-button link type="primary" icon="CopyDocument" @click="handleCopy(scope.row)" v-hasPermi="['otoconfig:gridGroup:add']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['otoconfig:gridGroup:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 添加或修改金刚位分组对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
      <el-form ref="gridGroupFormRef" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分组名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入分组名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="平台" prop="platform">
              <el-select v-model="form.platform" placeholder="请选择平台">
                <el-option label="APP" value="APP" />
                <el-option label="H5" value="H5" />
                <el-option label="小程序" value="MINI_PROGRAM" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择类型">
                <el-option label="默认" value="DEFAULT" />
                <el-option label="自定义" value="CUSTOM" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大项目数" prop="maxItems">
              <el-input-number v-model="form.maxItems" :min="1" :max="20" placeholder="最大项目数" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排序" prop="sortOrder">
              <el-input-number v-model="form.sortOrder" :min="0" placeholder="排序" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GridGroup" lang="ts">
import {
  listGridGroup,
  getGridGroup,
  delGridGroup,
  addGridGroup,
  updateGridGroup,
  updateGridGroupStatus,
  copyGridGroup,
  checkGridGroupNameUnique
} from '@/api/otoconfig/gridconfig';
import { GridGroupVO, GridGroupQuery, GridGroupForm } from '@/api/otoconfig/gridconfig/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const gridGroupList = ref<GridGroupVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const gridGroupFormRef = ref<ElFormInstance>();
const tableRef = ref();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GridGroupForm = {
  id: null,
  name: undefined,
  platform: undefined,
  type: 'CUSTOM',
  maxItems: 8,
  sortOrder: 0,
  status: 1,
  description: undefined
};

const data = reactive<PageData<GridGroupForm, GridGroupQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: undefined,
    platform: undefined,
    type: undefined,
    status: undefined,
    params: {}
  },
  rules: {
    name: [
      { required: true, message: '分组名称不能为空', trigger: 'blur' },
      {
        validator: async (rule: any, value: string, callback: any) => {
          if (value) {
            const isUnique = await checkGridGroupNameUnique(value, form.value.id || 0);
            if (!isUnique) {
              callback(new Error('分组名称已存在'));
            } else {
              callback();
            }
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ],
    platform: [{ required: true, message: '平台不能为空', trigger: 'change' }],
    type: [{ required: true, message: '类型不能为空', trigger: 'change' }],
    maxItems: [{ required: true, message: '最大项目数不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询金刚位分组列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGridGroup(queryParams.value);
  gridGroupList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  gridGroupFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: GridGroupVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加金刚位分组';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: GridGroupVO) => {
  reset();
  if (row) {
    tableRef.value?.clearSelection();
    tableRef.value?.toggleRowSelection(row, true);
    ids.value = [row.id];
    single.value = false;
    multiple.value = false;
  }
  const _id = row?.id || ids.value[0];
  if (_id == null || _id === 0) {
    proxy?.$modal.msgError('请选择要修改的分组');
    return;
  }
  const res = await getGridGroup(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改金刚位分组';
};

/** 提交按钮 */
const submitForm = () => {
  gridGroupFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGridGroup(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addGridGroup(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: GridGroupVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除金刚位分组编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delGridGroup(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'otoconfig/gridGroup/export',
    {
      ...queryParams.value
    },
    `gridGroup_${new Date().getTime()}.xlsx`
  );
};

/** 状态切换 */
const handleStatusChange = async (row: GridGroupVO) => {
  try {
    await updateGridGroupStatus(row.id, row.status);
    proxy?.$modal.msgSuccess('状态更新成功');
  } catch (error) {
    row.status = row.status === 1 ? 0 : 1; // 回滚状态
    proxy?.$modal.msgError('状态更新失败');
  }
};

/** 复制按钮操作 */
const handleCopy = async (row: GridGroupVO) => {
  await proxy?.$modal.confirm('是否确认复制该分组？');
  await copyGridGroup(row.id);
  proxy?.$modal.msgSuccess('复制成功');
  await getList();
};

/** 设为默认分组 */
const handleSetDefault = async () => {
  if (single.value) {
    proxy?.$modal.msgError('请选择要设为默认的分组');
    return;
  }
  const selectedGroup = gridGroupList.value.find((item) => item.id === ids.value[0]);
  if (selectedGroup?.type === 'DEFAULT') {
    proxy?.$modal.msgError('该分组已经是默认分组');
    return;
  }
  await proxy?.$modal.confirm('是否确认将该分组设为默认分组？');
  // 这里需要调用设为默认的API
  proxy?.$modal.msgSuccess('设置成功');
  await getList();
};

const handleRowClick = (row: GridGroupVO) => {
  tableRef.value?.toggleRowSelection(row);
};

onMounted(() => {
  getList();
});
</script>
