<template>
  <div class="p-2">
    <!-- 筛选条件 -->
    <el-card shadow="hover" class="mb-4">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="平台" prop="platform">
          <el-select v-model="queryParams.platform" placeholder="请选择平台" @change="handleQuery">
            <el-option label="APP" value="APP" />
            <el-option label="H5" value="H5" />
            <el-option label="小程序" value="MINI_PROGRAM" />
          </el-select>
        </el-form-item>
        <el-form-item label="分组" prop="groupId">
          <el-select v-model="queryParams.groupId" placeholder="请选择分组" @change="handleQuery" clearable>
            <el-option v-for="group in groupOptions" :key="group.id" :label="group.name" :value="group.id" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">查看预览</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 预览区域 -->
    <el-card shadow="never" v-loading="loading">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg font-medium">金刚位预览</span>
          <div class="flex items-center space-x-2">
            <el-tag v-if="queryParams.platform" type="primary">{{ getPlatformLabel(queryParams.platform) }}</el-tag>
            <el-tag v-if="selectedGroup" type="success">{{ selectedGroup.name }}</el-tag>
          </div>
        </div>
      </template>

      <!-- 无数据提示 -->
      <div v-if="!previewData.length && !loading" class="text-center py-20">
        <el-empty description="暂无数据">
          <template #image>
            <el-icon size="60" color="#d3d3d3">
              <Grid />
            </el-icon>
          </template>
          <el-button type="primary" @click="$router.push('/otoconfig/gridconfig/gridItem')">去配置金刚位</el-button>
        </el-empty>
      </div>

      <!-- 预览网格 -->
      <div v-else class="preview-container">
        <!-- 按分组显示 -->
        <div v-for="group in groupedPreviewData" :key="group.groupId" class="group-section mb-8">
          <div class="group-header mb-4">
            <h3 class="text-lg font-medium text-gray-800">{{ group.groupName }}</h3>
            <p class="text-sm text-gray-500">{{ group.description || '暂无描述' }}</p>
          </div>

          <!-- 金刚位网格 -->
          <div class="grid-container" :style="{ gridTemplateColumns: `repeat(${getGridColumns(group.items.length)}, 1fr)` }">
            <div v-for="item in group.items" :key="item.id" class="grid-item" @click="handleItemClick(item)">
              <div class="item-icon">
                <el-image
                  v-if="item.iconUrl"
                  :src="item.iconUrl"
                  fit="cover"
                  class="w-full h-full"
                  :preview-src-list="[item.iconUrl]"
                  :initial-index="0"
                  preview-teleported
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
                <div v-else class="image-placeholder">
                  <el-icon><Picture /></el-icon>
                </div>
              </div>
              <div class="item-title">{{ item.title }}</div>
              <div class="item-status">
                <el-tag v-if="item.status === 1" type="success" size="small">启用</el-tag>
                <el-tag v-else type="danger" size="small">禁用</el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 项目详情对话框 -->
    <el-dialog title="项目详情" v-model="detailDialog.visible" width="500px" append-to-body>
      <div v-if="selectedItem" class="item-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="项目标题">{{ selectedItem.title }}</el-descriptions-item>
          <el-descriptions-item label="平台">{{ getPlatformLabel(selectedItem.platform) }}</el-descriptions-item>
          <el-descriptions-item label="分类">{{ getCategoryLabel(selectedItem.category) }}</el-descriptions-item>
          <el-descriptions-item label="跳转链接">
            <el-link :href="selectedItem.jumpUrl" target="_blank" type="primary">{{ selectedItem.jumpUrl }}</el-link>
          </el-descriptions-item>
          <el-descriptions-item label="排序">{{ selectedItem.sortOrder }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag v-if="selectedItem.status === 1" type="success">启用</el-tag>
            <el-tag v-else type="danger">禁用</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="描述">{{ selectedItem.description || '暂无描述' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ parseTime(selectedItem.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialog.visible = false">关 闭</el-button>
          <el-button type="primary" @click="handleEditItem" v-hasPermi="['otoconfig:gridItem:edit']">编 辑</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GridPreview" lang="ts">
import { getGridPreviewByPlatform, getGridPreviewByGroupId } from '@/api/otoconfig/gridconfig';
import { listGridGroup } from '@/api/otoconfig/gridconfig';
import { GridPreviewVO, GridGroupVO, GridItemVO } from '@/api/otoconfig/gridconfig/types';
import { Grid, Picture } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const router = useRouter();

const loading = ref(false);
const previewData = ref<GridPreviewVO[]>([]);
const groupOptions = ref<GridGroupVO[]>([]);
const selectedGroup = ref<GridGroupVO | null>(null);
const selectedItem = ref<GridItemVO | null>(null);

const queryFormRef = ref<ElFormInstance>();

const queryParams = reactive({
  platform: 'APP',
  groupId: undefined as number | undefined
});

const detailDialog = reactive({
  visible: false
});

// 计算属性：按分组整理预览数据
const groupedPreviewData = computed(() => {
  const grouped = previewData.value.reduce(
    (acc, item) => {
      const groupId = item.groupId || 0;
      if (!acc[groupId]) {
        acc[groupId] = {
          groupId,
          groupName: item.groupName || '默认分组',
          description: item.groupDescription,
          items: []
        };
      }
      acc[groupId].items.push(item);
      return acc;
    },
    {} as Record<number, { groupId: number; groupName: string; description?: string; items: GridPreviewVO[] }>
  );

  return Object.values(grouped).sort((a, b) => a.groupId - b.groupId);
});

/** 获取预览数据 */
const getPreviewData = async () => {
  loading.value = true;
  try {
    let res;
    if (queryParams.groupId) {
      res = await getGridPreviewByGroupId(queryParams.groupId);
    } else {
      res = await getGridPreviewByPlatform(queryParams.platform);
    }
    previewData.value = res.data || [];
  } catch (error) {
    proxy?.$modal.msgError('获取预览数据失败');
    previewData.value = [];
  } finally {
    loading.value = false;
  }
};

/** 获取分组选项 */
const getGroupOptions = async () => {
  try {
    const res = await listGridGroup({
      pageNum: 1,
      pageSize: 1000,
      platform: queryParams.platform,
      status: 1 // 只获取启用的分组
    });
    groupOptions.value = res.rows;
  } catch (error) {
    console.error('获取分组选项失败:', error);
  }
};

/** 搜索按钮操作 */
const handleQuery = () => {
  // 更新选中的分组信息
  selectedGroup.value = queryParams.groupId ? groupOptions.value.find((g) => g.id === queryParams.groupId) || null : null;

  getPreviewData();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.platform = 'APP';
  queryParams.groupId = undefined;
  selectedGroup.value = null;
  handleQuery();
};

/** 计算网格列数 */
const getGridColumns = (itemCount: number) => {
  if (itemCount <= 4) return 4;
  if (itemCount <= 8) return 4;
  return 5;
};

/** 获取平台标签 */
const getPlatformLabel = (platform: string) => {
  const labels: Record<string, string> = {
    'APP': 'APP',
    'H5': 'H5',
    'MINI_PROGRAM': '小程序'
  };
  return labels[platform] || platform;
};

/** 获取分类标签 */
const getCategoryLabel = (category: string) => {
  const labels: Record<string, string> = {
    'HOT': '热门',
    'NEW': '新品',
    'RECOMMEND': '推荐'
  };
  return labels[category] || category;
};

/** 项目点击事件 */
const handleItemClick = (item: GridPreviewVO) => {
  selectedItem.value = item;
  detailDialog.visible = true;
};

/** 编辑项目 */
const handleEditItem = () => {
  if (selectedItem.value) {
    router.push({
      path: '/otoconfig/gridconfig/gridItem',
      query: { id: selectedItem.value.id }
    });
  }
};

// 监听平台变化，重新获取分组选项
watch(
  () => queryParams.platform,
  () => {
    queryParams.groupId = undefined;
    selectedGroup.value = null;
    getGroupOptions();
  },
  { immediate: true }
);

onMounted(() => {
  getGroupOptions();
  handleQuery();
});
</script>

<style scoped>
.preview-container {
  max-width: 1200px;
  margin: 0 auto;
}

.group-section {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background: #fafafa;
}

.group-header {
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 12px;
}

.grid-container {
  display: grid;
  gap: 16px;
  padding: 20px 0;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 120px;
}

.grid-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.item-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 8px;
  border-radius: 8px;
  overflow: hidden;
}

.image-error,
.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  color: #c0c4cc;
  font-size: 20px;
}

.item-title {
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  margin-bottom: 4px;
  color: #303133;
  line-height: 1.4;
  word-break: break-all;
}

.item-status {
  margin-top: auto;
}

.item-detail {
  padding: 0 20px;
}

@media (max-width: 768px) {
  .grid-container {
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 12px;
  }

  .grid-item {
    padding: 12px;
    min-height: 100px;
  }

  .item-icon {
    width: 40px;
    height: 40px;
  }

  .item-title {
    font-size: 12px;
  }
}
</style>
