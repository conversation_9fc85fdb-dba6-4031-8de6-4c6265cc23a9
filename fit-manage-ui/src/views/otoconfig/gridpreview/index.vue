<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="search">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="平台" prop="platform">
            <el-select v-model="queryParams.platform" placeholder="请选择平台" clearable style="width: 240px">
              <el-option label="移动端" value="mobile" />
              <el-option label="PC端" value="pc" />
              <el-option label="小程序" value="miniprogram" />
            </el-select>
          </el-form-item>
          <el-form-item label="分组" prop="groupId">
            <el-select v-model="queryParams.groupId" placeholder="请选择分组" clearable style="width: 240px">
              <el-option v-for="group in groupOptions" :key="group.id" :label="group.name" :value="group.id" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="View" @click="handlePreview">预览</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" icon="Refresh" @click="handleRefresh">刷新</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getPreviewData" />
        </el-row>
      </template>

      <!-- 平台选择标签 -->
      <el-tabs v-model="activePlatform" @tab-click="handlePlatformChange">
        <el-tab-pane label="移动端" name="mobile">
          <div class="preview-container mobile-preview">
            <div v-if="previewData.mobile && previewData.mobile.length > 0" class="grid-container">
              <div v-for="group in previewData.mobile" :key="group.groupId" class="grid-group">
                <div class="group-header">
                  <h3>{{ group.groupName }}</h3>
                  <span class="item-count">{{ group.items.length }}个项目</span>
                </div>
                <div class="grid-items mobile-grid">
                  <div v-for="item in group.items" :key="item.itemId" class="grid-item mobile-item">
                    <div class="item-icon">
                      <el-image v-if="item.icon" :src="item.icon" fit="cover" />
                      <div v-else class="placeholder-icon">图</div>
                    </div>
                    <div class="item-title">{{ item.title }}</div>
                    <div class="item-status">
                      <el-tag v-if="item.isActive === 1" type="success" size="small">启用</el-tag>
                      <el-tag v-else type="danger" size="small">禁用</el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <el-empty v-else description="暂无移动端金刚位数据" />
          </div>
        </el-tab-pane>

        <el-tab-pane label="PC端" name="pc">
          <div class="preview-container pc-preview">
            <div v-if="previewData.pc && previewData.pc.length > 0" class="grid-container">
              <div v-for="group in previewData.pc" :key="group.groupId" class="grid-group">
                <div class="group-header">
                  <h3>{{ group.groupName }}</h3>
                  <span class="item-count">{{ group.items.length }}个项目</span>
                </div>
                <div class="grid-items pc-grid">
                  <div v-for="item in group.items" :key="item.itemId" class="grid-item pc-item">
                    <div class="item-icon">
                      <el-image v-if="item.icon" :src="item.icon" fit="cover" />
                      <div v-else class="placeholder-icon">图</div>
                    </div>
                    <div class="item-content">
                      <div class="item-title">{{ item.title }}</div>
                      <div class="item-description">{{ item.category || '暂无描述' }}</div>
                    </div>
                    <div class="item-status">
                      <el-tag v-if="item.isActive === 1" type="success" size="small">启用</el-tag>
                      <el-tag v-else type="danger" size="small">禁用</el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <el-empty v-else description="暂无PC端金刚位数据" />
          </div>
        </el-tab-pane>

        <el-tab-pane label="小程序" name="miniprogram">
          <div class="preview-container miniprogram-preview">
            <div v-if="previewData.miniprogram && previewData.miniprogram.length > 0" class="grid-container">
              <div v-for="group in previewData.miniprogram" :key="group.groupId" class="grid-group">
                <div class="group-header">
                  <h3>{{ group.groupName }}</h3>
                  <span class="item-count">{{ group.items.length }}个项目</span>
                </div>
                <div class="grid-items miniprogram-grid">
                  <div v-for="item in group.items" :key="item.itemId" class="grid-item miniprogram-item">
                    <div class="item-icon">
                      <el-image v-if="item.icon" :src="item.icon" fit="cover" />
                      <div v-else class="placeholder-icon">图</div>
                    </div>
                    <div class="item-title">{{ item.title }}</div>
                    <div class="item-status">
                      <el-tag v-if="item.isActive === 1" type="success" size="small">启用</el-tag>
                      <el-tag v-else type="danger" size="small">禁用</el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <el-empty v-else description="暂无小程序金刚位数据" />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 预览对话框 -->
    <el-dialog v-model="previewDialog.visible" :title="previewDialog.title" width="80%" append-to-body>
      <div class="preview-dialog-content">
        <div class="preview-header">
          <el-select v-model="previewPlatform" placeholder="选择预览平台" style="width: 200px" @change="handlePreviewPlatformChange">
            <el-option label="移动端" value="mobile" />
            <el-option label="PC端" value="pc" />
            <el-option label="小程序" value="miniprogram" />
          </el-select>
        </div>
        <div class="preview-content">
          <div v-if="currentPreviewData && currentPreviewData.length > 0" class="preview-grid">
            <div v-for="group in currentPreviewData" :key="group.groupId" class="preview-group">
              <h4>{{ group.groupName }}</h4>
              <div class="preview-items">
                <div v-for="item in group.items" :key="item.id" class="preview-item">
                  <el-image v-if="item.icon" :src="item.icon" style="width: 50px; height: 50px" fit="cover" />
                  <div class="preview-item-info">
                    <div class="preview-item-title">{{ item.title }}</div>
                    <div class="preview-item-desc">{{ item.description || '暂无描述' }}</div>
                  </div>
                  <el-tag v-if="item.status === 1" type="success" size="small">启用</el-tag>
                  <el-tag v-else type="danger" size="small">禁用</el-tag>
                </div>
              </div>
            </div>
          </div>
          <el-empty v-else description="暂无预览数据" />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="previewDialog.visible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GridPreview" lang="ts">
import { getGridPreviewByPlatform, getGridPreviewByGroupId } from '@/api/otoconfig/gridconfig';
import { listGridGroup } from '@/api/otoconfig/gridconfig';
import { GridPreviewVO, GridGroupVO, GridPreviewGroupVO, GridPreviewGroupDetailVO } from '@/api/otoconfig/gridconfig/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const loading = ref(true);
const showSearch = ref(true);
const activePlatform = ref('mobile');
const groupOptions = ref<GridGroupVO[]>([]);
const previewData = ref<Record<string, GridPreviewGroupDetailVO[]>>({
  mobile: [],
  pc: [],
  miniprogram: []
});

const queryFormRef = ref<ElFormInstance>();

const queryParams = ref({
  platform: '',
  groupId: undefined
});

const previewDialog = reactive({
  visible: false,
  title: '金刚位预览'
});

const previewPlatform = ref('mobile');
const currentPreviewData = ref<GridPreviewGroupDetailVO[]>([]);

/** 查询分组选项 */
const getGroupOptions = async () => {
  const res = await listGridGroup({
    pageNum: 1,
    pageSize: 1000
  });
  groupOptions.value = res.rows.map((group) => ({
    ...group,
    name: group.groupName
  }));
};

/** 获取预览数据 */
const getPreviewData = async () => {
  loading.value = true;
  try {
    // 获取所有平台的预览数据
    const platforms = ['mobile', 'pc', 'miniprogram'];
    for (const platform of platforms) {
      const res = await getGridPreviewByPlatform(platform);
      const previewVO = res.data;
      if (previewVO && previewVO.groups) {
        // 将预览数据转换为分组格式 - 修复：直接使用group内部的items
        previewData.value[platform] = previewVO.groups.map((group) => ({
          groupId: group.groupId,
          groupName: group.groupName,
          items: group.items || [] // 直接使用group内部的items数组
        }));
      } else {
        previewData.value[platform] = [];
      }
    }
  } catch (error) {
    console.error('获取预览数据失败:', error);
  } finally {
    loading.value = false;
  }
};

/** 搜索按钮操作 */
const handleQuery = () => {
  getPreviewData();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 平台切换 */
const handlePlatformChange = (tab: any) => {
  activePlatform.value = tab.name;
};

/** 预览按钮操作 */
const handlePreview = () => {
  previewPlatform.value = activePlatform.value;
  currentPreviewData.value = previewData.value[activePlatform.value] || [];
  previewDialog.visible = true;
};

/** 预览平台切换 */
const handlePreviewPlatformChange = () => {
  currentPreviewData.value = previewData.value[previewPlatform.value] || [];
};

/** 刷新按钮操作 */
const handleRefresh = () => {
  getPreviewData();
  proxy?.$modal.msgSuccess('刷新成功');
};

onMounted(() => {
  getPreviewData();
  getGroupOptions();
});
</script>

<style scoped>
.preview-container {
  min-height: 400px;
}

.grid-container {
  padding: 20px;
}

.grid-group {
  margin-bottom: 30px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background-color: #fafafa;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.group-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.item-count {
  color: #909399;
  font-size: 12px;
}

.grid-items {
  display: grid;
  gap: 15px;
}

.mobile-grid {
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
}

.pc-grid {
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.miniprogram-grid {
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: white;
  transition: all 0.3s ease;
}

.grid-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.mobile-item {
  min-height: 100px;
}

.pc-item {
  flex-direction: row;
  text-align: left;
  min-height: 80px;
}

.miniprogram-item {
  min-height: 120px;
}

.item-icon {
  margin-bottom: 8px;
}

.pc-item .item-icon {
  margin-bottom: 0;
  margin-right: 15px;
  flex-shrink: 0;
}

.item-icon .el-image {
  width: 40px;
  height: 40px;
  border-radius: 4px;
}

.pc-item .item-icon .el-image {
  width: 50px;
  height: 50px;
}

.placeholder-icon {
  width: 40px;
  height: 40px;
  background-color: #f5f7fa;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  font-size: 12px;
}

.pc-item .placeholder-icon {
  width: 50px;
  height: 50px;
}

.item-content {
  flex: 1;
}

.item-title {
  font-size: 12px;
  color: #303133;
  text-align: center;
  margin-bottom: 5px;
  font-weight: 500;
}

.pc-item .item-title {
  text-align: left;
  font-size: 14px;
  margin-bottom: 5px;
}

.item-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.item-status {
  margin-top: 5px;
}

.pc-item .item-status {
  margin-top: 0;
  margin-left: 10px;
  flex-shrink: 0;
}

.preview-dialog-content {
  max-height: 600px;
  overflow-y: auto;
}

.preview-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.preview-grid {
  padding: 20px;
}

.preview-group {
  margin-bottom: 25px;
}

.preview-group h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.preview-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 10px;
}

.preview-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.preview-item-info {
  flex: 1;
  margin-left: 10px;
}

.preview-item-title {
  font-size: 13px;
  color: #303133;
  font-weight: 500;
  margin-bottom: 3px;
}

.preview-item-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.3;
}
</style>
