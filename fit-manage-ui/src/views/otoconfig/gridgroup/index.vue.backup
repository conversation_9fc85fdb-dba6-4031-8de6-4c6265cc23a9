<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="search">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="分组名称" prop="groupName">
            <el-input v-model="queryParams.groupName" placeholder="请输入分组名称" clearable style="width: 240px" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="平台" prop="platform">
            <el-select v-model="queryParams.platform" placeholder="请选择平台" clearable style="width: 240px">
              <el-option label="移动端" value="mobile" />
              <el-option label="PC端" value="pc" />
              <el-option label="小程序" value="miniprogram" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="isActive">
            <el-select v-model="queryParams.isActive" placeholder="请选择状态" clearable style="width: 240px">
              <el-option label="启用" value="1" />
              <el-option label="禁用" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['oto:gridgroup:add']" type="primary" plain icon="Plus" @click="handleAdd"> 新增 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['oto:gridgroup:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()">
              修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['oto:gridgroup:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()">
              删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['oto:gridgroup:export']" type="warning" plain icon="Download" @click="handleExport"> 导出 </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
        </el-row>
      </template>

      <el-table v-loading="loading" :data="gridGroupList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" width="80" />
        <el-table-column label="分组名称" align="center" prop="groupName" :show-overflow-tooltip="true" />

        <el-table-column label="平台" align="center" prop="platform">
          <template #default="scope">
            <dict-tag :options="platformOptions" :value="scope.row.platform" />
          </template>
        </el-table-column>
        <el-table-column label="类型" align="center" prop="groupType">
          <template #default="scope">
            <dict-tag :options="typeOptions" :value="scope.row.groupType" />
          </template>
        </el-table-column>
        <el-table-column label="排序" align="center" prop="sortOrder" width="80" />

        <el-table-column label="状态" align="center" prop="isActive">
          <template #default="scope">
            <el-switch v-model="scope.row.isActive" :active-value="1" :inactive-value="0" @change="(value) => handleStatusChange(scope.row, value)" />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['oto:gridgroup:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)" />
            </el-tooltip>
            <el-tooltip content="复制" placement="top">
              <el-button v-hasPermi="['oto:gridgroup:copy']" link type="success" icon="CopyDocument" @click="handleCopy(scope.row)" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['oto:gridgroup:remove']" link type="danger" icon="Delete" @click="handleDelete(scope.row)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>

    <!-- 添加或修改金刚位分组对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="600px" append-to-body>
      <el-form ref="gridGroupFormRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="分组名称" prop="groupName">
              <el-input v-model="form.groupName" placeholder="请输入分组名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="平台" prop="platform">
              <el-select v-model="form.platform" placeholder="请选择平台">
                <el-option label="移动端" value="mobile" />
                <el-option label="PC端" value="pc" />
                <el-option label="小程序" value="miniprogram" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="类型" prop="groupType">
              <el-select v-model="form.groupType" placeholder="请选择类型">
                <el-option label="默认" value="default" />
                <el-option label="自定义" value="custom" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sortOrder">
              <el-input-number v-model="form.sortOrder" :min="0" :max="9999" controls-position="right" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="isActive">
              <el-radio-group v-model="form.isActive">
                <el-radio value="1">启用</el-radio>
                <el-radio value="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大数量" prop="maxItems">
              <el-input-number v-model="form.maxItems" :min="1" :max="50" controls-position="right" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GridGroup" lang="ts">
import {
  listGridGroup,
  getGridGroup,
  delGridGroup,
  addGridGroup,
  updateGridGroup,
  updateGridGroupStatus,
  copyGridGroup
} from '@/api/otoconfig/gridconfig';
import { GridGroupVO, GridGroupForm, GridGroupQuery } from '@/api/otoconfig/gridconfig/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const gridGroupList = ref<GridGroupVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const gridGroupList = ref<GridGroupVO[]>([]);
const isDataInitializing = ref(false);

const queryFormRef = ref<ElFormInstance>();
const gridGroupFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GridGroupForm = {
  id: undefined,
  groupName: '',
  platform: '',
  groupType: 'default',
  description: '',
  sortOrder: 0,
  isActive: '1',
  maxItems: 10,
  layoutConfig: '',
  remark: ''
};
const data = reactive<PageData<GridGroupForm, GridGroupQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    groupName: '',
    platform: '',
    isActive: ''
  },
  rules: {
    groupName: [{ required: true, message: '分组名称不能为空', trigger: 'blur' }],
    platform: [{ required: true, message: '平台不能为空', trigger: 'change' }],
    groupType: [{ required: true, message: '类型不能为空', trigger: 'change' }],
    sortOrder: [{ required: true, message: '排序不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 平台选项
const platformOptions = ref([
  { label: '移动端', value: 'mobile' },
  { label: 'PC端', value: 'pc' },
  { label: '小程序', value: 'miniprogram' }
]);

// 类型选项
const typeOptions = ref([
  { label: '默认', value: 'default' },
  { label: '自定义', value: 'custom' }
]);

/** 查询金刚位分组列表 */
const getList = async () => {
  loading.value = true;
  isDataInitializing.value = true;
  try {
    const res = await listGridGroup(queryParams.value);
    gridGroupList.value = res.data.rows;
    total.value = res.data.total;
  } catch (error) {
    console.error('获取金刚位分组列表失败:', error);
    gridGroupList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
    // 延迟重置数据初始化标志，确保Vue响应式更新完成
    setTimeout(() => {
      isDataInitializing.value = false;
    }, 100);
  }
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  gridGroupFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: GridGroupVO[]) => {
  ids.value = selection.map((item) => item.id!);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加金刚位分组';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: GridGroupVO) => {
  reset();
  const id = row?.id || ids.value[0];
  const res = await getGridGroup(id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改金刚位分组';
};

/** 提交按钮 */
const submitForm = () => {
  gridGroupFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      if (form.value.id) {
        await updateGridGroup(form.value);
        proxy?.$modal.msgSuccess('修改成功');
      } else {
        await addGridGroup(form.value);
        proxy?.$modal.msgSuccess('新增成功');
      }
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: GridGroupVO) => {
  const delIds = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除金刚位分组编号为"' + delIds + '"的数据项？');
  await delGridGroup(delIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.$download.excel(listGridGroup, queryParams.value, `gridgroup_${new Date().getTime()}.xlsx`);
};

/** 状态修改 */
const handleStatusChange = async (row: GridGroupVO, newValue: number) => {
  const text = newValue === 1 ? '启用' : '停用';
  console.log(`[GridGroup] 状态变更操作: ${text} 分组 "${row.groupName}" (ID: ${row.id})`);

  // 防止页面初始化时自动触发
  if (loading.value || isDataInitializing.value) {
    console.log(`[GridGroup] 页面加载或数据初始化中，跳过状态变更确认`);
    return;
  }

  try {
    await proxy?.$modal.confirm(`确认要${text}"${row.groupName}"吗？`);
    await updateGridGroupStatus(row.id!, newValue);
    console.log(`[GridGroup] 状态变更成功: ${text} 分组 "${row.groupName}" (ID: ${row.id})`);
    proxy?.$modal.msgSuccess(`${text}成功`);
  } catch (error) {
    console.log(`[GridGroup] 状态变更取消或失败: ${text} 分组 "${row.groupName}" (ID: ${row.id})`, error);
    // 恢复原来的状态
    row.isActive = row.isActive === 0 ? 1 : 0;
  }
};

/** 复制按钮操作 */
const handleCopy = async (row: GridGroupVO) => {
  await copyGridGroup(row.id!);
  proxy?.$modal.msgSuccess('复制成功');
  await getList();
};

onMounted(() => {
  getList();
});
</script>
