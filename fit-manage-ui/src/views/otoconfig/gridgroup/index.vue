<template>
  <div class="grid-group-container">
    <!-- 搜索区域 -->
    <transition
      :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave"
    >
      <div v-show="showSearch" class="search-section">
        <el-form
          ref="queryFormRef"
          :model="queryParams"
          :inline="true"
          label-width="80px"
          class="search-form"
        >
          <el-form-item label="分组名称" prop="groupName">
            <el-input
              v-model="queryParams.groupName"
              placeholder="请输入分组名称"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="分组类型" prop="groupType">
            <el-select
              v-model="queryParams.groupType"
              placeholder="请选择分组类型"
              clearable
              style="width: 150px"
            >
              <el-option label="默认分组" value="default" />
              <el-option label="推荐分组" value="recommend" />
            </el-select>
          </el-form-item>
          <el-form-item label="平台类型" prop="platform">
            <el-select
              v-model="queryParams.platform"
              placeholder="请选择平台"
              clearable
              style="width: 150px"
            >
              <el-option label="全平台" value="all" />
              <el-option label="iOS" value="ios" />
              <el-option label="Android" value="android" />
              <el-option label="H5" value="h5" />
              <el-option label="小程序" value="miniprogram" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option label="正常" value="0" />
              <el-option label="停用" value="1" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">
              搜索
            </el-button>
            <el-button icon="Refresh" @click="resetQuery">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </transition>

    <!-- 主内容区域 -->
    <el-card shadow="never" class="main-card">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <el-icon class="title-icon"><Grid /></el-icon>
            <span>金刚位分组管理</span>
          </div>
          <div class="header-actions">
            <el-button
              v-hasPermi="['front:config:gridGroup:add']"
              type="primary"
              icon="Plus"
              @click="handleAdd"
            >
              新增分组
            </el-button>
            <el-button
              v-hasPermi="['front:config:gridGroup:edit']"
              type="success"
              plain
              icon="Edit"
              :disabled="single"
              @click="handleUpdate()"
            >
              修改
            </el-button>
            <el-button
              v-hasPermi="['front:config:gridGroup:remove']"
              type="danger"
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleDelete()"
            >
              删除
            </el-button>
            <el-button
              v-hasPermi="['front:config:gridGroup:export']"
              type="warning"
              plain
              icon="Download"
              @click="handleExport"
            >
              导出
            </el-button>
            <right-toolbar
              v-model:showSearch="showSearch"
              @queryTable="getList"
            />
          </div>
        </div>
      </template>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="gridGroupList"
        row-key="groupId"
        class="data-table"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />

        <el-table-column
          label="分组ID"
          align="center"
          prop="groupId"
          width="80"
          sortable
        />

        <el-table-column
          label="分组名称"
          align="left"
          prop="groupName"
          min-width="150"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <div class="group-name-cell">
              <el-icon class="group-icon"><Grid /></el-icon>
              <span class="group-name">{{ scope.row.groupName }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          label="分组类型"
          align="center"
          prop="groupType"
          width="120"
        >
          <template #default="scope">
            <el-tag
              :type="getGroupTypeTagType(scope.row.groupType)"
              size="small"
            >
              {{ getGroupTypeLabel(scope.row.groupType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          label="平台类型"
          align="center"
          prop="platform"
          width="120"
        >
          <template #default="scope">
            <el-tag
              :type="getPlatformTagType(scope.row.platform)"
              size="small"
            >
              {{ getPlatformLabel(scope.row.platform) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          label="排序值"
          align="center"
          prop="sortOrder"
          width="80"
          sortable
        />

        <el-table-column
          label="最大项目数"
          align="center"
          prop="maxItems"
          width="100"
        >
          <template #default="scope">
            <span>{{ scope.row.maxItems || '无限制' }}</span>
          </template>
        </el-table-column>

        <el-table-column
          label="状态"
          align="center"
          prop="status"
          width="100"
        >
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="'0'"
              :inactive-value="'1'"
              :loading="scope.row.statusLoading"
              @change="(value) => handleStatusChange(scope.row, value)"
            />
          </template>
        </el-table-column>

        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          width="160"
          sortable
        >
          <template #default="scope">
            <span>{{ formatTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          align="center"
          width="180"
          fixed="right"
          class-name="action-column"
        >
          <template #default="scope">
            <div class="action-buttons">
              <el-tooltip content="编辑分组" placement="top">
                <el-button
                  v-hasPermi="['front:config:gridGroup:edit']"
                  link
                  type="primary"
                  icon="Edit"
                  @click="handleUpdate(scope.row)"
                />
              </el-tooltip>
              <el-tooltip content="复制分组" placement="top">
                <el-button
                  v-hasPermi="['front:config:gridGroup:add']"
                  link
                  type="success"
                  icon="CopyDocument"
                  @click="handleCopy(scope.row)"
                />
              </el-tooltip>
              <el-tooltip content="查看项目" placement="top">
                <el-button
                  v-hasPermi="['front:config:gridItem:list']"
                  link
                  type="info"
                  icon="View"
                  @click="handleViewItems(scope.row)"
                />
              </el-tooltip>
              <el-tooltip content="删除分组" placement="top">
                <el-button
                  v-hasPermi="['front:config:gridGroup:remove']"
                  link
                  type="danger"
                  icon="Delete"
                  @click="handleDelete(scope.row)"
                />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="pagination-container"
        @pagination="getList"
      />
    </el-card>

    <!-- 新增/编辑分组对话框 -->
    <el-dialog
      v-model="dialog.visible"
      :title="dialog.title"
      width="700px"
      append-to-body
      :close-on-click-modal="false"
      class="group-dialog"
    >
      <el-form
        ref="gridGroupFormRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        class="group-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分组名称" prop="groupName">
              <el-input
                v-model="form.groupName"
                placeholder="请输入分组名称"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分组类型" prop="groupType">
              <el-select
                v-model="form.groupType"
                placeholder="请选择分组类型"
                style="width: 100%"
              >
                <el-option label="默认分组" value="default" />
                <el-option label="推荐分组" value="recommend" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="平台类型" prop="platform">
              <el-select
                v-model="form.platform"
                placeholder="请选择平台类型"
                style="width: 100%"
              >
                <el-option label="全平台" value="all" />
                <el-option label="iOS" value="ios" />
                <el-option label="Android" value="android" />
                <el-option label="H5" value="h5" />
                <el-option label="小程序" value="miniprogram" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序值" prop="sortOrder">
              <el-input-number
                v-model="form.sortOrder"
                :min="0"
                :max="9999"
                controls-position="right"
                style="width: 100%"
                placeholder="数值越小越靠前"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio value="0">正常</el-radio>
                <el-radio value="1">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大项目数" prop="maxItems">
              <el-input-number
                v-model="form.maxItems"
                :min="1"
                :max="100"
                controls-position="right"
                style="width: 100%"
                placeholder="留空表示无限制"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="布局配置" prop="layoutConfig">
          <el-input
            v-model="form.layoutConfig"
            type="textarea"
            :rows="3"
            placeholder="请输入布局配置（JSON格式），如：{&quot;columns&quot;: 4, &quot;rows&quot;: 2}"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取消</el-button>
          <el-button
            type="primary"
            :loading="submitLoading"
            @click="submitForm"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 复制分组对话框 -->
    <el-dialog
      v-model="copyDialog.visible"
      title="复制分组"
      width="400px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="copyFormRef"
        :model="copyForm"
        :rules="copyRules"
        label-width="80px"
      >
        <el-form-item label="新分组名" prop="groupName">
          <el-input
            v-model="copyForm.groupName"
            placeholder="请输入新分组名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="copyDialog.visible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="copyLoading"
            @click="submitCopy"
          >
            确定复制
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GridGroup" lang="ts">
import { ref, reactive, computed, onMounted, getCurrentInstance, nextTick } from 'vue'
import type { ElFormInstance, ElMessageBox } from 'element-plus'
import { ElMessage, ElMessageBox as MessageBox } from 'element-plus'
import { Grid, View } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { parseTime } from '@/utils/ruoyi'

// API导入
import { gridGroupApi } from '@/api/otoconfig/gridconfig'

// API方法别名
const {
  list: listGridGroup,
  getInfo: getGridGroup,
  add: addGridGroup,
  update: updateGridGroup,
  remove: delGridGroup,
  updateStatus: updateGridGroupStatus,
  copy: copyGridGroup
} = gridGroupApi

// 类型定义
interface GridGroupVO {
  groupId: number
  groupName: string
  groupType: string
  platform: string
  sortOrder: number
  maxItems?: number
  layoutConfig?: string
  status: string
  remark?: string
  createTime: string
  updateTime: string
  statusLoading?: boolean
}

interface GridGroupForm {
  groupId?: number
  groupName: string
  groupType: string
  platform: string
  sortOrder: number
  maxItems?: number
  layoutConfig?: string
  status: string
  remark?: string
}

interface GridGroupQuery {
  pageNum: number
  pageSize: number
  groupName?: string
  groupType?: string
  platform?: string
  status?: string
}

interface CopyForm {
  groupName: string
}

// 组件实例
const { proxy } = getCurrentInstance()!
const router = useRouter()

// 响应式数据
const gridGroupList = ref<GridGroupVO[]>([])
const loading = ref(false)
const showSearch = ref(true)
const ids = ref<number[]>([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const submitLoading = ref(false)
const copyLoading = ref(false)

// 表单引用
const queryFormRef = ref<ElFormInstance>()
const gridGroupFormRef = ref<ElFormInstance>()
const copyFormRef = ref<ElFormInstance>()

// 查询参数
const queryParams = reactive<GridGroupQuery>({
  pageNum: 1,
  pageSize: 10,
  groupName: '',
  groupType: '',
  platform: '',
  status: ''
})

// 对话框状态
const dialog = reactive({
  visible: false,
  title: ''
})

const copyDialog = reactive({
  visible: false
})

// 表单数据
const form = reactive<GridGroupForm>({
  groupName: '',
  groupType: 'default',
  platform: 'all',
  sortOrder: 0,
  maxItems: undefined,
  layoutConfig: '',
  status: '0',
  remark: ''
})

// 复制表单
const copyForm = reactive<CopyForm>({
  groupName: ''
})

// 当前复制的分组ID
const copyGroupId = ref<number>()

// 表单验证规则
const rules = reactive({
  groupName: [
    { required: true, message: '分组名称不能为空', trigger: 'blur' },
    { min: 2, max: 50, message: '分组名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  groupType: [
    { required: true, message: '分组类型不能为空', trigger: 'change' }
  ],
  platform: [
    { required: true, message: '平台类型不能为空', trigger: 'change' }
  ],
  sortOrder: [
    { required: true, message: '排序值不能为空', trigger: 'blur' },
    { type: 'number', min: 0, max: 9999, message: '排序值必须在 0 到 9999 之间', trigger: 'blur' }
  ]
})

// 复制表单验证规则
const copyRules = reactive({
  groupName: [
    { required: true, message: '新分组名称不能为空', trigger: 'blur' },
    { min: 2, max: 50, message: '分组名称长度在 2 到 50 个字符', trigger: 'blur' }
  ]
})

// 查询分组列表
const getList = async () => {
  loading.value = true
  try {
    const response = await listGridGroup(queryParams)
    gridGroupList.value = response.rows || []
    total.value = response.total || 0
  } catch (error) {
    console.error('获取分组列表失败:', error)
    ElMessage.error('获取分组列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置搜索
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

// 多选处理
const handleSelectionChange = (selection: GridGroupVO[]) => {
  ids.value = selection.map(item => item.groupId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    groupName: '',
    groupType: 'default',
    platform: 'all',
    sortOrder: 0,
    maxItems: undefined,
    layoutConfig: '',
    status: '0',
    remark: ''
  })
  nextTick(() => {
    gridGroupFormRef.value?.clearValidate()
  })
}

// 新增分组
const handleAdd = () => {
  resetForm()
  dialog.visible = true
  dialog.title = '新增金刚位分组'
}

// 编辑分组
const handleUpdate = async (row?: GridGroupVO) => {
  resetForm()
  const groupId = row?.groupId || ids.value[0]

  try {
    const response = await getGridGroup(groupId)
    const data = response.data

    Object.assign(form, {
      groupId: data.groupId,
      groupName: data.groupName,
      groupType: data.groupType,
      platform: data.platform,
      sortOrder: data.sortOrder,
      maxItems: data.maxItems,
      layoutConfig: data.layoutConfig,
      status: data.status,
      remark: data.remark
    })

    dialog.visible = true
    dialog.title = '编辑金刚位分组'
  } catch (error) {
    console.error('获取分组详情失败:', error)
    ElMessage.error('获取分组详情失败')
  }
}

// 工具函数
const getGroupTypeLabel = (type: string): string => {
  const typeMap: Record<string, string> = {
    default: '默认分组',
    recommend: '推荐分组'
  }
  return typeMap[type] || type
}

const getGroupTypeTagType = (type: string): string => {
  const typeMap: Record<string, string> = {
    default: '',
    recommend: 'success'
  }
  return typeMap[type] || ''
}

const getPlatformLabel = (platform: string): string => {
  const platformMap: Record<string, string> = {
    all: '全平台',
    ios: 'iOS',
    android: 'Android',
    h5: 'H5',
    miniprogram: '小程序'
  }
  return platformMap[platform] || platform
}

const getPlatformTagType = (platform: string): string => {
  const typeMap: Record<string, string> = {
    all: 'info',
    ios: 'primary',
    android: 'success',
    h5: 'warning',
    miniprogram: 'danger'
  }
  return typeMap[platform] || ''
}

const formatTime = (time: string): string => {
  return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}') || ''
}

// 取消
const cancel = () => {
  dialog.visible = false
  resetForm()
}

// 提交表单
const submitForm = async () => {
  if (!gridGroupFormRef.value) return

  const valid = await gridGroupFormRef.value.validate().catch(() => false)
  if (!valid) return

  submitLoading.value = true

  try {
    if (form.groupId) {
      await updateGridGroup(form)
      ElMessage.success('修改成功')
    } else {
      await addGridGroup(form)
      ElMessage.success('新增成功')
    }

    dialog.visible = false
    await getList()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 删除分组
const handleDelete = async (row?: GridGroupVO) => {
  const groupIds = row ? [row.groupId] : ids.value
  const groupNames = row ? [row.groupName] : gridGroupList.value
    .filter(item => groupIds.includes(item.groupId))
    .map(item => item.groupName)

  try {
    await MessageBox.confirm(
      `确认删除分组"${groupNames.join('、')}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await delGridGroup(groupIds)
    ElMessage.success('删除成功')
    await getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 状态切换
const handleStatusChange = async (row: GridGroupVO, value: string) => {
  const text = value === '0' ? '启用' : '停用'

  // 设置loading状态
  row.statusLoading = true

  try {
    await MessageBox.confirm(
      `确认要${text}分组"${row.groupName}"吗？`,
      '状态确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // API参数：id, isActive (0=启用, 1=停用)
    const isActive = value === '0' ? 0 : 1
    await updateGridGroupStatus(row.groupId, isActive)
    ElMessage.success(`${text}成功`)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('状态切换失败:', error)
      ElMessage.error('操作失败')
    }
    // 恢复原状态
    row.status = row.status === '0' ? '1' : '0'
  } finally {
    row.statusLoading = false
  }
}

// 复制分组
const handleCopy = (row: GridGroupVO) => {
  copyGroupId.value = row.groupId
  copyForm.groupName = `${row.groupName}_副本`
  copyDialog.visible = true
}

// 提交复制
const submitCopy = async () => {
  if (!copyFormRef.value) return

  const valid = await copyFormRef.value.validate().catch(() => false)
  if (!valid) return

  copyLoading.value = true

  try {
    await copyGridGroup(copyGroupId.value!, copyForm.groupName)
    ElMessage.success('复制成功')
    copyDialog.visible = false
    await getList()
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  } finally {
    copyLoading.value = false
  }
}

// 查看分组项目
const handleViewItems = (row: GridGroupVO) => {
  router.push({
    path: '/otoconfig/gridconfig',
    query: {
      groupId: row.groupId,
      groupName: row.groupName
    }
  })
}

// 导出
const handleExport = () => {
  proxy?.download(
    'front/config/gridGroup/export',
    queryParams,
    `gridgroup_${new Date().getTime()}.xlsx`
  )
}

// 组件挂载
onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
.grid-group-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.search-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  .el-form-item {
    margin-bottom: 16px;
  }
}

.main-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;

    .header-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: #303133;

      .title-icon {
        margin-right: 8px;
        color: #409eff;
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }
}

.data-table {
  .group-name-cell {
    display: flex;
    align-items: center;

    .group-icon {
      margin-right: 8px;
      color: #409eff;
    }

    .group-name {
      font-weight: 500;
    }
  }

  .action-column {
    .action-buttons {
      display: flex;
      justify-content: center;
      gap: 4px;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.group-dialog {
  .group-form {
    .el-form-item {
      margin-bottom: 20px;
    }
  }

  .dialog-footer {
    text-align: right;

    .el-button {
      margin-left: 12px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .grid-group-container {
    padding: 10px;
  }

  .search-form {
    .el-form-item {
      margin-bottom: 12px;
    }
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 12px;

    .header-actions {
      width: 100%;
      justify-content: flex-start;
      flex-wrap: wrap;
    }
  }

  .data-table {
    .el-table__body-wrapper {
      overflow-x: auto;
    }
  }
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .grid-group-container {
    background-color: #1a1a1a;
  }

  .search-section,
  .main-card {
    background-color: #2d2d2d;
    border-color: #404040;
  }
}
</style>
