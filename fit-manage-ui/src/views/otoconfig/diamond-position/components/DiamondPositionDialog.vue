<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑金刚位' : '新增金刚位'"
    width="600px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="金刚位名称" prop="title">
        <el-input
          v-model="form.title"
          placeholder="请输入金刚位名称"
          maxlength="20"
          show-word-limit
          clearable
        />
      </el-form-item>

      <el-form-item label="图标类型" prop="iconType">
        <el-select
          v-model="form.iconType"
          placeholder="请选择图标类型"
          @change="handleIconTypeChange"
          style="width: 100%"
        >
          <el-option label="URL链接" value="url" />
          <el-option label="表情符号" value="emoji" />
          <el-option label="SVG代码" value="svg" />
          <el-option label="Base64编码" value="base64" />
        </el-select>
      </el-form-item>

      <el-form-item label="图标内容" prop="icon">
        <!-- URL类型 -->
        <div v-if="form.iconType === 'url'" class="icon-input-group">
          <div class="input-with-upload">
            <el-input
              v-model="form.icon"
              placeholder="请输入图标URL地址，如：https://example.com/icon.png"
              @input="updateIconPreview"
            />
            <el-upload
              :action="uploadAction"
              :show-file-list="false"
              :on-success="handleImageUploadSuccess"
              :on-error="handleUploadError"
              :before-upload="beforeImageUpload"
              accept="image/*"
              style="margin-left: 8px"
            >
              <el-button type="primary" size="small" :loading="uploading">
                <el-icon><Upload /></el-icon>
                上传图片
              </el-button>
            </el-upload>
          </div>
          <div class="form-help">支持网络链接、本地路径或上传图片文件</div>
        </div>

        <!-- 表情符号类型 -->
        <div v-else-if="form.iconType === 'emoji'" class="icon-input-group">
          <el-input
            v-model="form.icon"
            placeholder="请输入表情符号，如：💰 🔧 💳"
            maxlength="10"
            show-word-limit
            @input="updateIconPreview"
          />
          <div class="form-help">支持Unicode表情符号，长度不超过10个字符</div>
        </div>

        <!-- SVG类型 -->
        <div v-else-if="form.iconType === 'svg'" class="icon-input-group">
          <div class="textarea-with-upload">
            <el-input
              v-model="form.icon"
              type="textarea"
              :rows="4"
              placeholder="请输入SVG代码，如：<svg viewBox=&quot;0 0 24 24&quot;>...</svg>"
              @input="updateIconPreview"
            />
            <el-upload
              :action="uploadAction"
              :show-file-list="false"
              :on-success="handleSvgUploadSuccess"
              :on-error="handleUploadError"
              :before-upload="beforeSvgUpload"
              accept=".svg,image/svg+xml"
              class="upload-btn-overlay"
            >
              <el-button type="primary" size="small" :loading="uploading">
                <el-icon><Upload /></el-icon>
                上传SVG
              </el-button>
            </el-upload>
          </div>
          <div class="form-help">支持SVG矢量图代码或上传SVG文件</div>
        </div>

        <!-- Base64类型 -->
        <div v-else-if="form.iconType === 'base64'" class="icon-input-group">
          <div class="textarea-with-upload">
            <el-input
              v-model="form.icon"
              type="textarea"
              :rows="3"
              placeholder="请输入Base64编码，如：data:image/png;base64,iVBORw0KGgo..."
              @input="updateIconPreview"
            />
            <el-upload
              :action="uploadAction"
              :show-file-list="false"
              :on-success="handleBase64UploadSuccess"
              :on-error="handleUploadError"
              :before-upload="beforeImageUpload"
              accept="image/*"
              class="upload-btn-overlay"
            >
              <el-button type="primary" size="small" :loading="uploading">
                <el-icon><Upload /></el-icon>
                转换Base64
              </el-button>
            </el-upload>
          </div>
          <div class="form-help">支持Base64编码的图片或上传图片转换</div>
        </div>
      </el-form-item>

      <!-- 图标预览 -->
      <el-form-item label="图标预览">
        <div class="icon-preview">
          <DiamondIcon
            v-if="form.icon"
            :icon="form.icon"
            :icon-type="form.iconType || 'url'"
            :size="64"
            :alt="form.title"
            border
          />
          <div v-else class="preview-placeholder">
            <el-icon size="32"><Picture /></el-icon>
            <span>暂无预览</span>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="跳转链接" prop="route">
        <el-input
          v-model="form.route"
          placeholder="请输入跳转链接，如：/pages/service 或 https://example.com"
          clearable
        />
      </el-form-item>

      <el-form-item label="平台" prop="platform">
        <el-select v-model="form.platform" placeholder="请选择平台" style="width: 100%">
          <el-option label="全平台" value="ALL" />
          <el-option label="iOS" value="IOS" />
          <el-option label="Android" value="ANDROID" />
          <el-option label="H5" value="H5" />
        </el-select>
      </el-form-item>

      <el-form-item label="排序序号" prop="sortOrder">
        <el-input-number
          v-model="form.sortOrder"
          :min="1"
          :max="999"
          placeholder="排序序号"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="状态" prop="isActive">
        <el-radio-group v-model="form.isActive">
          <el-radio label="1">启用</el-radio>
          <el-radio label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入描述信息（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="标签">
        <el-checkbox v-model="form.isHot">热门</el-checkbox>
        <el-checkbox v-model="form.isNew" style="margin-left: 16px">新品</el-checkbox>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="submitting">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, Picture } from '@element-plus/icons-vue'
import type { FormInstance, FormRules, UploadProps } from 'element-plus'

// 组件导入
import DiamondIcon from './DiamondIcon.vue'

// 类型定义
interface FormData {
  id?: number
  title: string
  icon: string
  iconType: 'url' | 'svg' | 'base64' | 'emoji'
  route: string
  platform: string
  sortOrder: number
  isActive: string
  description: string
  isHot: boolean
  isNew: boolean
}

// Props
interface Props {
  modelValue: boolean
  formData?: any
  isEdit?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  formData: null,
  isEdit: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: FormData]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const uploading = ref(false)
const submitting = ref(false)
const uploadAction = ref('/api/upload') // 实际项目中应该从配置获取

// 表单数据
const form = reactive<FormData>({
  title: '',
  icon: '',
  iconType: 'url',
  route: '',
  platform: 'ALL',
  sortOrder: 1,
  isActive: '1',
  description: '',
  isHot: false,
  isNew: false
})

// 表单验证规则
const rules: FormRules = {
  title: [
    { required: true, message: '请输入金刚位名称', trigger: 'blur' },
    { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
  ],
  iconType: [
    { required: true, message: '请选择图标类型', trigger: 'change' }
  ],
  icon: [
    { required: true, message: '请输入图标内容', trigger: 'blur' },
    { validator: validateIcon, trigger: 'blur' }
  ],
  route: [
    { required: true, message: '请输入跳转链接', trigger: 'blur' }
  ],
  platform: [
    { required: true, message: '请选择平台', trigger: 'change' }
  ],
  sortOrder: [
    { required: true, message: '请输入排序序号', trigger: 'blur' },
    { type: 'number', min: 1, max: 999, message: '排序序号范围为 1-999', trigger: 'blur' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 图标验证函数
function validateIcon(rule: any, value: string, callback: Function) {
  if (!value) {
    callback(new Error('请输入图标内容'))
    return
  }

  switch (form.iconType) {
    case 'url':
      if (!/^(https?:\/\/|\/)/i.test(value)) {
        callback(new Error('URL类型图标必须以http://、https://或/开头'))
      } else {
        callback()
      }
      break
    case 'svg':
      if (!value.includes('<svg') || !value.includes('</svg>')) {
        callback(new Error('SVG类型图标必须包含完整的<svg>标签'))
      } else {
        callback()
      }
      break
    case 'base64':
      if (!value.startsWith('data:image/')) {
        callback(new Error('Base64类型图标必须以data:image/开头'))
      } else {
        callback()
      }
      break
    case 'emoji':
      if (value.length > 10) {
        callback(new Error('表情符号长度不能超过10个字符'))
      } else {
        callback()
      }
      break
    default:
      callback()
  }
}

// 方法
const handleIconTypeChange = () => {
  form.icon = ''
  updateIconPreview()
}

const updateIconPreview = () => {
  // 图标预览更新逻辑
}

const beforeImageUpload: UploadProps['beforeUpload'] = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }

  uploading.value = true
  return true
}

const beforeSvgUpload: UploadProps['beforeUpload'] = (file) => {
  const isSvg = file.type === 'image/svg+xml' || file.name.endsWith('.svg')
  const isLt1M = file.size / 1024 / 1024 < 1

  if (!isSvg) {
    ElMessage.error('只能上传SVG文件!')
    return false
  }
  if (!isLt1M) {
    ElMessage.error('SVG文件大小不能超过 1MB!')
    return false
  }

  uploading.value = true
  return true
}

const handleImageUploadSuccess = (response: any) => {
  uploading.value = false
  if (response.success) {
    form.icon = response.data.url
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error('图片上传失败')
  }
}

const handleSvgUploadSuccess = (response: any) => {
  uploading.value = false
  if (response.success) {
    form.icon = response.data.svgContent
    ElMessage.success('SVG文件上传成功')
  } else {
    ElMessage.error('SVG文件上传失败')
  }
}

const handleBase64UploadSuccess = (response: any) => {
  uploading.value = false
  if (response.success) {
    form.icon = response.data.base64
    ElMessage.success('图片转换成功')
  } else {
    ElMessage.error('图片转换失败')
  }
}

const handleUploadError = () => {
  uploading.value = false
  ElMessage.error('文件上传失败')
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true
    
    emit('confirm', { ...form })
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    title: '',
    icon: '',
    iconType: 'url',
    route: '',
    platform: 'ALL',
    sortOrder: 1,
    isActive: '1',
    description: '',
    isHot: false,
    isNew: false
  })
}

// 监听表单数据变化
watch(() => props.formData, (newData) => {
  if (newData && props.modelValue) {
    Object.assign(form, {
      ...newData,
      isHot: newData.isHot === '1' || newData.isHot === true,
      isNew: newData.isNew === '1' || newData.isNew === true
    })
  }
}, { immediate: true, deep: true })

// 监听对话框显示状态
watch(() => props.modelValue, (visible) => {
  if (!visible) {
    resetForm()
  }
})
</script>

<style scoped>
.icon-input-group {
  width: 100%;
}

.input-with-upload {
  display: flex;
  align-items: center;
  width: 100%;
}

.input-with-upload .el-input {
  flex: 1;
}

.textarea-with-upload {
  position: relative;
}

.upload-btn-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 1;
}

.form-help {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.icon-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border: 1px dashed #dcdfe6;
  border-radius: 8px;
  background: #fafafa;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  font-size: 12px;
  gap: 4px;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .input-with-upload {
    flex-direction: column;
    gap: 8px;
  }

  .input-with-upload .el-upload {
    margin-left: 0;
    align-self: stretch;
  }

  .upload-btn-overlay {
    position: static;
    margin-top: 8px;
  }
}
</style>
