<template>
  <div 
    class="diamond-icon" 
    :class="iconClass"
    :style="iconStyle"
  >
    <!-- URL类型图标 -->
    <img
      v-if="iconType === 'url'"
      :src="icon"
      :alt="alt"
      :style="imageStyle"
      @load="handleImageLoad"
      @error="handleImageError"
    />

    <!-- 表情符号类型 -->
    <span
      v-else-if="iconType === 'emoji'"
      class="emoji-icon"
      :style="emojiStyle"
    >
      {{ icon }}
    </span>

    <!-- SVG类型图标 -->
    <div
      v-else-if="iconType === 'svg'"
      class="svg-icon"
      :style="svgStyle"
      v-html="sanitizedSvg"
    />

    <!-- Base64类型图标 -->
    <img
      v-else-if="iconType === 'base64'"
      :src="icon"
      :alt="alt"
      :style="imageStyle"
      @load="handleImageLoad"
      @error="handleImageError"
    />

    <!-- 默认/错误状态图标 -->
    <div
      v-else
      class="default-icon"
      :style="defaultIconStyle"
    >
      <el-icon :size="Math.floor(size * 0.6)">
        <Picture />
      </el-icon>
    </div>

    <!-- 加载状态 -->
    <div
      v-if="loading"
      class="loading-overlay"
      :style="overlayStyle"
    >
      <el-icon class="loading-spinner">
        <Loading />
      </el-icon>
    </div>

    <!-- 错误状态 -->
    <div
      v-if="error"
      class="error-overlay"
      :style="overlayStyle"
      @click="handleRetry"
    >
      <el-icon>
        <Warning />
      </el-icon>
      <span class="error-text">加载失败</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Picture, Loading, Warning } from '@element-plus/icons-vue'
import DOMPurify from 'dompurify'
import type { IconType } from '@/types/diamond-position'

// Props
interface Props {
  /** 图标内容 */
  icon: string
  /** 图标类型 */
  iconType: IconType
  /** 图标大小 */
  size?: number
  /** 替代文本 */
  alt?: string
  /** 是否圆形 */
  round?: boolean
  /** 是否显示边框 */
  border?: boolean
  /** 背景颜色 */
  background?: string
  /** 是否可点击 */
  clickable?: boolean
  /** 是否禁用 */
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 48,
  alt: '图标',
  round: false,
  border: false,
  background: 'transparent',
  clickable: false,
  disabled: false
})

// Emits
const emit = defineEmits<{
  'click': [event: MouseEvent]
  'load': []
  'error': [error: Event]
}>()

// 响应式数据
const loading = ref(false)
const error = ref(false)

// 计算属性
const iconClass = computed(() => ({
  'diamond-icon--round': props.round,
  'diamond-icon--border': props.border,
  'diamond-icon--clickable': props.clickable,
  'diamond-icon--disabled': props.disabled,
  'diamond-icon--loading': loading.value,
  'diamond-icon--error': error.value
}))

const iconStyle = computed(() => ({
  width: `${props.size}px`,
  height: `${props.size}px`,
  background: props.background,
  cursor: props.clickable ? 'pointer' : 'default'
}))

const imageStyle = computed(() => ({
  width: '100%',
  height: '100%',
  objectFit: 'cover' as const,
  borderRadius: props.round ? '50%' : '4px'
}))

const emojiStyle = computed(() => ({
  fontSize: `${Math.floor(props.size * 0.7)}px`,
  lineHeight: '1'
}))

const svgStyle = computed(() => ({
  width: '100%',
  height: '100%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center'
}))

const defaultIconStyle = computed(() => ({
  width: '100%',
  height: '100%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  background: '#f5f7fa',
  color: '#c0c4cc',
  borderRadius: props.round ? '50%' : '4px'
}))

const overlayStyle = computed(() => ({
  position: 'absolute' as const,
  top: '0',
  left: '0',
  right: '0',
  bottom: '0',
  display: 'flex',
  flexDirection: 'column' as const,
  alignItems: 'center',
  justifyContent: 'center',
  background: 'rgba(255, 255, 255, 0.9)',
  borderRadius: props.round ? '50%' : '4px'
}))

// 清理和验证SVG内容
const sanitizedSvg = computed(() => {
  if (props.iconType !== 'svg') return ''
  
  try {
    // 使用DOMPurify清理SVG内容，防止XSS攻击
    const cleaned = DOMPurify.sanitize(props.icon, {
      USE_PROFILES: { svg: true, svgFilters: true }
    })
    
    // 确保SVG有正确的viewBox和尺寸
    const parser = new DOMParser()
    const doc = parser.parseFromString(cleaned, 'image/svg+xml')
    const svgElement = doc.querySelector('svg')
    
    if (svgElement) {
      // 设置SVG的宽高为100%
      svgElement.setAttribute('width', '100%')
      svgElement.setAttribute('height', '100%')
      
      // 如果没有viewBox，尝试从width和height推断
      if (!svgElement.getAttribute('viewBox')) {
        const width = svgElement.getAttribute('width') || '24'
        const height = svgElement.getAttribute('height') || '24'
        svgElement.setAttribute('viewBox', `0 0 ${width} ${height}`)
      }
      
      return svgElement.outerHTML
    }
    
    return cleaned
  } catch (error) {
    console.error('SVG清理失败:', error)
    return ''
  }
})

// 方法
const handleImageLoad = () => {
  loading.value = false
  error.value = false
  emit('load')
}

const handleImageError = (event: Event) => {
  loading.value = false
  error.value = true
  emit('error', event)
}

const handleRetry = () => {
  if (props.iconType === 'url' || props.iconType === 'base64') {
    error.value = false
    loading.value = true
    
    // 重新加载图片
    const img = new Image()
    img.onload = handleImageLoad
    img.onerror = handleImageError
    img.src = props.icon
  }
}

const handleClick = (event: MouseEvent) => {
  if (props.clickable && !props.disabled) {
    emit('click', event)
  }
}

// 监听图标变化，重置状态
watch([() => props.icon, () => props.iconType], () => {
  error.value = false
  loading.value = false
  
  // 对于图片类型，开始加载
  if (props.iconType === 'url' || props.iconType === 'base64') {
    loading.value = true
  }
}, { immediate: true })

// 验证图标内容
const validateIcon = () => {
  switch (props.iconType) {
    case 'url':
      return /^(https?:\/\/|\/)/i.test(props.icon)
    case 'svg':
      return props.icon.includes('<svg') && props.icon.includes('</svg>')
    case 'base64':
      return props.icon.startsWith('data:image/')
    case 'emoji':
      return props.icon.length <= 10
    default:
      return false
  }
}

// 如果图标内容无效，显示错误状态
watch([() => props.icon, () => props.iconType], () => {
  if (!validateIcon()) {
    error.value = true
  }
}, { immediate: true })
</script>

<style scoped>
.diamond-icon {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: all 0.3s ease;
}

.diamond-icon--round {
  border-radius: 50%;
}

.diamond-icon--border {
  border: 1px solid #dcdfe6;
}

.diamond-icon--clickable {
  cursor: pointer;
}

.diamond-icon--clickable:hover {
  transform: scale(1.05);
}

.diamond-icon--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.diamond-icon--disabled:hover {
  transform: none;
}

.diamond-icon--loading {
  pointer-events: none;
}

.diamond-icon--error .default-icon {
  background: #fef0f0;
  color: #f56c6c;
}

.emoji-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
}

.svg-icon {
  color: inherit;
}

.svg-icon :deep(svg) {
  width: 100%;
  height: 100%;
  fill: currentColor;
}

.loading-overlay {
  background: rgba(255, 255, 255, 0.9);
}

.loading-spinner {
  animation: rotate 2s linear infinite;
  color: #409eff;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.error-overlay {
  background: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
  cursor: pointer;
  transition: background 0.3s ease;
}

.error-overlay:hover {
  background: rgba(245, 108, 108, 0.2);
}

.error-text {
  font-size: 10px;
  margin-top: 2px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .diamond-icon--clickable:hover {
    transform: none;
  }
  
  .diamond-icon--clickable:active {
    transform: scale(0.95);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .diamond-icon--border {
    border-width: 2px;
    border-color: #000;
  }
  
  .default-icon {
    border: 1px solid #000;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .diamond-icon,
  .loading-spinner,
  .error-overlay {
    transition: none;
    animation: none;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .diamond-icon--border {
    border-color: #4c4d4f;
  }
  
  .default-icon {
    background: #2d2d2d;
    color: #8c8c8c;
  }
  
  .loading-overlay {
    background: rgba(0, 0, 0, 0.9);
  }
  
  .error-overlay {
    background: rgba(245, 108, 108, 0.2);
  }
}
</style>
