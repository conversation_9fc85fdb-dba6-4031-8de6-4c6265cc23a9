<template>
  <div class="diamond-grid-container">
    <!-- 网格头部工具栏 -->
    <div class="grid-toolbar">
      <div class="toolbar-left">
        <el-checkbox
          v-model="selectAll"
          :indeterminate="isIndeterminate"
          @change="handleSelectAll"
        >
          全选
        </el-checkbox>
        <span class="selected-count">
          已选择 {{ selectedItems.length }} 项
        </span>
      </div>
      
      <div class="toolbar-right">
        <el-button-group>
          <el-button
            :type="sortMode ? 'primary' : 'default'"
            @click="toggleSortMode"
          >
            <el-icon><Sort /></el-icon>
            {{ sortMode ? '完成排序' : '拖拽排序' }}
          </el-button>
          <el-button @click="handlePreview">
            <el-icon><View /></el-icon>
            预览效果
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 空状态 -->
    <div v-else-if="items.length === 0" class="empty-container">
      <el-empty description="暂无金刚位数据">
        <el-button type="primary" @click="$emit('create')">
          立即创建
        </el-button>
      </el-empty>
    </div>

    <!-- 网格内容 -->
    <div
      v-else
      class="diamond-grid"
      :class="{ 'sort-mode': sortMode, 'is-dragging': isDragging }"
    >
      <div
        v-for="item in items"
        :key="item.id"
        :data-id="item.id"
        class="diamond-item"
        :class="{
          'selected': isSelected(item),
          'disabled': item.status === 0,
          'draggable': sortMode && canDrag(item)
        }"
        :draggable="sortMode && canDrag(item)"
        @click="handleItemClick(item, $event)"
        @dragstart="handleDragStart($event, item)"
        @dragover="handleDragOver"
        @dragenter="handleDragEnter($event, item)"
        @dragleave="handleDragLeave"
        @drop="handleDrop($event, item)"
        @dragend="handleDragEnd"
        @touchstart="handleTouchStart($event, item)"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      >
        <!-- 选择框 -->
        <div class="item-checkbox" @click.stop>
          <el-checkbox
            :model-value="isSelected(item)"
            @change="handleItemSelect(item, $event)"
          />
        </div>

        <!-- 拖拽手柄 -->
        <div v-if="sortMode" class="drag-handle">
          <el-icon><Rank /></el-icon>
        </div>

        <!-- 图标区域 -->
        <div class="item-icon">
          <DiamondIcon
            :icon="item.icon"
            :icon-type="item.iconType"
            :size="64"
            :alt="item.name"
          />
          
          <!-- 状态标识 -->
          <div v-if="item.status === 0" class="status-badge disabled">
            禁用
          </div>
          <div v-if="item.isHot" class="status-badge hot">
            热门
          </div>
          <div v-if="item.isNew" class="status-badge new">
            新品
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="item-content">
          <div class="item-name" :title="item.name">
            {{ item.name }}
          </div>
          <div class="item-meta">
            <el-tag
              :type="getPlatformTagType(item.platform)"
              size="small"
            >
              {{ getPlatformLabel(item.platform) }}
            </el-tag>
            <el-tag
              :type="getIconTypeTagType(item.iconType)"
              size="small"
              class="ml-1"
            >
              {{ getIconTypeLabel(item.iconType) }}
            </el-tag>
          </div>
          <div class="item-description" :title="item.description">
            {{ item.description || '暂无描述' }}
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="item-actions" @click.stop>
          <el-button-group>
            <el-button
              size="small"
              type="primary"
              @click="$emit('edit', item)"
            >
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button
              size="small"
              :type="item.status === 1 ? 'warning' : 'success'"
              @click="$emit('toggle-status', item, item.status === 1 ? 0 : 1)"
            >
              <el-icon>
                <component :is="item.status === 1 ? 'Hide' : 'View'" />
              </el-icon>
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="$emit('delete', item)"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </el-button-group>
        </div>

        <!-- 排序序号 -->
        <div class="sort-order">
          {{ item.sortOrder }}
        </div>
      </div>
    </div>

    <!-- 拖拽提示 -->
    <div v-if="sortMode" class="sort-tips">
      <el-alert
        title="拖拽排序模式"
        description="拖拽金刚位卡片可以调整显示顺序，完成后点击"完成排序"按钮保存"
        type="info"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  Sort,
  View,
  Edit,
  Delete,
  Hide,
  Rank
} from '@element-plus/icons-vue'

// 组件导入
import DiamondIcon from './DiamondIcon.vue'

// 组合式函数导入
import { useDragAndDrop, useTouchDragAndDrop } from '@/composables/useDragAndDrop'
import type { 
  DiamondPosition, 
  UpdateSortOrderRequest,
  PlatformType,
  IconType 
} from '@/types/diamond-position'
import { PLATFORMS, ICON_TYPES } from '@/types/diamond-position'

// Props
interface Props {
  items: DiamondPosition[]
  loading: boolean
  selected: DiamondPosition[]
}

const props = withDefaults(defineProps<Props>(), {
  items: () => [],
  loading: false,
  selected: () => []
})

// Emits
const emit = defineEmits<{
  'update:selected': [items: DiamondPosition[]]
  'edit': [item: DiamondPosition]
  'delete': [item: DiamondPosition]
  'toggle-status': [item: DiamondPosition, status: 0 | 1]
  'sort-change': [newOrder: UpdateSortOrderRequest[]]
  'create': []
}>()

// 响应式数据
const sortMode = ref(false)
const selectedItems = ref<DiamondPosition[]>([])

// 拖拽功能
const itemsRef = computed(() => props.items)
const {
  isDragging,
  handleDragStart,
  handleDragOver,
  handleDragEnter,
  handleDragLeave,
  handleDrop,
  handleDragEnd,
  canDrag
} = useDragAndDrop(itemsRef, (newOrder) => {
  emit('sort-change', newOrder)
})

// 触摸拖拽功能
const {
  handleTouchStart,
  handleTouchMove,
  handleTouchEnd
} = useTouchDragAndDrop(itemsRef, (newOrder) => {
  emit('sort-change', newOrder)
})

// 计算属性
const selectAll = computed({
  get: () => selectedItems.value.length === props.items.length && props.items.length > 0,
  set: (value: boolean) => {
    if (value) {
      selectedItems.value = [...props.items]
    } else {
      selectedItems.value = []
    }
  }
})

const isIndeterminate = computed(() => {
  const selectedCount = selectedItems.value.length
  return selectedCount > 0 && selectedCount < props.items.length
})

// 监听选中项变化
watch(selectedItems, (newValue) => {
  emit('update:selected', newValue)
}, { deep: true })

watch(() => props.selected, (newValue) => {
  selectedItems.value = newValue
}, { deep: true })

// 方法
const isSelected = (item: DiamondPosition): boolean => {
  return selectedItems.value.some(selected => selected.id === item.id)
}

const handleSelectAll = (value: boolean) => {
  selectAll.value = value
}

const handleItemSelect = (item: DiamondPosition, selected: boolean) => {
  if (selected) {
    if (!isSelected(item)) {
      selectedItems.value.push(item)
    }
  } else {
    selectedItems.value = selectedItems.value.filter(selected => selected.id !== item.id)
  }
}

const handleItemClick = (item: DiamondPosition, event: MouseEvent) => {
  if (sortMode.value) return
  
  // 如果按住Ctrl/Cmd键，则切换选择状态
  if (event.ctrlKey || event.metaKey) {
    handleItemSelect(item, !isSelected(item))
  } else {
    // 否则单选
    selectedItems.value = [item]
  }
}

const toggleSortMode = () => {
  sortMode.value = !sortMode.value
  if (!sortMode.value) {
    selectedItems.value = []
  }
}

const handlePreview = () => {
  // 触发预览事件
  emit('create') // 临时使用create事件，实际应该是preview事件
}

// 获取平台标签类型
const getPlatformTagType = (platform: PlatformType): string => {
  const platformMap = {
    'ALL': 'primary',
    'IOS': 'success',
    'ANDROID': 'warning',
    'H5': 'info'
  }
  return platformMap[platform] || 'default'
}

// 获取平台标签文本
const getPlatformLabel = (platform: PlatformType): string => {
  const platformOption = PLATFORMS.find(p => p.value === platform)
  return platformOption?.label || platform
}

// 获取图标类型标签类型
const getIconTypeTagType = (iconType: IconType): string => {
  const typeMap = {
    'url': 'primary',
    'emoji': 'success',
    'svg': 'warning',
    'base64': 'info'
  }
  return typeMap[iconType] || 'default'
}

// 获取图标类型标签文本
const getIconTypeLabel = (iconType: IconType): string => {
  const typeOption = ICON_TYPES.find(t => t.value === iconType)
  return typeOption?.label || iconType
}
</script>

<style scoped>
.diamond-grid-container {
  padding: 20px;
}

.grid-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 0;
  border-bottom: 1px solid #ebeef5;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.selected-count {
  color: #909399;
  font-size: 14px;
}

.loading-container {
  padding: 20px;
}

.empty-container {
  padding: 40px 20px;
}

.diamond-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.diamond-item {
  position: relative;
  background: #fff;
  border: 2px solid #ebeef5;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
}

.diamond-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.diamond-item.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.diamond-item.disabled {
  opacity: 0.6;
  background: #f5f7fa;
}

.diamond-item.draggable {
  cursor: move;
}

.diamond-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.diamond-item.drag-over {
  border-color: #67c23a;
  background: #f0f9ff;
}

.item-checkbox {
  position: absolute;
  top: 12px;
  left: 12px;
  z-index: 2;
}

.drag-handle {
  position: absolute;
  top: 12px;
  right: 12px;
  color: #909399;
  cursor: move;
  z-index: 2;
}

.item-icon {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
  margin: 20px 0 16px 0;
}

.status-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  color: #fff;
  font-weight: 500;
}

.status-badge.disabled {
  background: #f56c6c;
}

.status-badge.hot {
  background: #e6a23c;
}

.status-badge.new {
  background: #67c23a;
}

.item-content {
  text-align: center;
  margin-bottom: 16px;
}

.item-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-meta {
  margin-bottom: 8px;
}

.item-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-actions {
  display: flex;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.diamond-item:hover .item-actions {
  opacity: 1;
}

.sort-order {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  background: #409eff;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
}

.sort-tips {
  margin-top: 20px;
}

.sort-mode .diamond-item {
  cursor: move;
}

.sort-mode .diamond-item:hover {
  transform: none;
}

.is-dragging .diamond-item:not(.dragging) {
  pointer-events: none;
}

.ml-1 {
  margin-left: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .diamond-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
  }
  
  .diamond-item {
    padding: 12px;
  }
  
  .item-icon {
    height: 60px;
    margin: 16px 0 12px 0;
  }
}

@media (max-width: 480px) {
  .diamond-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
  }
  
  .grid-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
}
</style>
