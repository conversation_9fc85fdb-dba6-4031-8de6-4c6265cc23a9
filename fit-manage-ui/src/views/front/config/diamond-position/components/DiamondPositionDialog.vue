<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="left"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入金刚位名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="跳转链接" prop="url">
            <el-input
              v-model="formData.url"
              placeholder="请输入跳转链接"
              maxlength="500"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="图标类型" prop="iconType">
            <el-select
              v-model="formData.iconType"
              placeholder="请选择图标类型"
              style="width: 100%"
              @change="handleIconTypeChange"
            >
              <el-option
                v-for="type in iconTypeOptions"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              >
                <div class="icon-type-option">
                  <span>{{ type.label }}</span>
                  <span class="option-desc">{{ type.description }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 图标内容输入区域 -->
      <el-form-item label="图标内容" prop="icon">
        <!-- URL类型 -->
        <div v-if="formData.iconType === 'url'" class="icon-input-container">
          <el-input
            v-model="formData.icon"
            placeholder="请输入图片URL地址"
            maxlength="500"
          >
            <template #append>
              <el-upload
                :show-file-list="false"
                :before-upload="handleImageUpload"
                accept="image/*"
                :disabled="uploading"
              >
                <el-button :loading="uploading">
                  <el-icon><Upload /></el-icon>
                  上传图片
                </el-button>
              </el-upload>
            </template>
          </el-input>
        </div>

        <!-- SVG类型 -->
        <div v-else-if="formData.iconType === 'svg'" class="icon-input-container">
          <el-input
            v-model="formData.icon"
            type="textarea"
            :rows="6"
            placeholder="请输入SVG代码或上传SVG文件"
            maxlength="10000"
            show-word-limit
          />
          <div class="upload-area">
            <el-upload
              :show-file-list="false"
              :before-upload="handleSvgUpload"
              accept=".svg"
              :disabled="uploading"
            >
              <el-button :loading="uploading">
                <el-icon><Upload /></el-icon>
                上传SVG文件
              </el-button>
            </el-upload>
          </div>
        </div>

        <!-- Base64类型 -->
        <div v-else-if="formData.iconType === 'base64'" class="icon-input-container">
          <el-input
            v-model="formData.icon"
            type="textarea"
            :rows="4"
            placeholder="请粘贴Base64编码或上传图片文件"
            maxlength="50000"
            show-word-limit
          />
          <div class="upload-area">
            <el-upload
              :show-file-list="false"
              :before-upload="handleBase64Upload"
              accept="image/*"
              :disabled="uploading"
            >
              <el-button :loading="uploading">
                <el-icon><Upload /></el-icon>
                上传图片转Base64
              </el-button>
            </el-upload>
          </div>
        </div>

        <!-- 表情符号类型 -->
        <div v-else-if="formData.iconType === 'emoji'" class="icon-input-container">
          <el-input
            v-model="formData.icon"
            placeholder="请输入表情符号，如：💰 🔧 💳"
            maxlength="10"
            show-word-limit
          >
            <template #append>
              <el-button @click="showEmojiPicker">
                <el-icon><Smile /></el-icon>
                选择表情
              </el-button>
            </template>
          </el-input>
        </div>
      </el-form-item>

      <!-- 图标预览 -->
      <el-form-item label="图标预览">
        <div class="icon-preview-container">
          <DiamondIcon
            v-if="formData.icon && formData.iconType"
            :icon="formData.icon"
            :icon-type="formData.iconType"
            :size="64"
            :alt="formData.name"
          />
          <div v-else class="preview-placeholder">
            <el-icon><Picture /></el-icon>
            <span>暂无预览</span>
          </div>
        </div>
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排序权重" prop="sortOrder">
            <el-input-number
              v-model="formData.sortOrder"
              :min="0"
              :max="9999"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述信息" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入描述信息（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ mode === 'create' ? '新增' : '更新' }}
        </el-button>
      </div>
    </template>

    <!-- 表情符号选择器 -->
    <EmojiPicker
      v-model:visible="emojiPickerVisible"
      @select="handleEmojiSelect"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Upload,
  Smile,
  Picture
} from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'

// 组件导入
import DiamondIcon from './DiamondIcon.vue'
import EmojiPicker from './EmojiPicker.vue'

// API导入
import { uploadImage, uploadSvg, validateIcon } from '@/api/diamond-position'
import type {
  DiamondPosition,
  CreateDiamondPositionRequest,
  UpdateDiamondPositionRequest,
  IconType
} from '@/types/diamond-position'
import { ICON_TYPES, ICON_VALIDATION_RULES } from '@/types/diamond-position'

// Props
interface Props {
  visible: boolean
  formData: DiamondPosition | null
  mode: 'create' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  formData: null,
  mode: 'create'
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'confirm': [data: CreateDiamondPositionRequest | UpdateDiamondPositionRequest]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const dialogVisible = ref(false)
const submitting = ref(false)
const uploading = ref(false)
const emojiPickerVisible = ref(false)

// 表单数据
const formData = reactive<CreateDiamondPositionRequest>({
  name: '',
  icon: '',
  iconType: 'url',
  url: '',
  sortOrder: 0,
  status: 1,
  description: ''
})

// 计算属性
const dialogTitle = computed(() => {
  return props.mode === 'create' ? '新增金刚位' : '编辑金刚位'
})

const iconTypeOptions = computed(() => ICON_TYPES)

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入金刚位名称', trigger: 'blur' },
    { min: 1, max: 50, message: '名称长度在1到50个字符', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入跳转链接', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ],
  iconType: [
    { required: true, message: '请选择图标类型', trigger: 'change' }
  ],
  icon: [
    { required: true, message: '请输入图标内容', trigger: 'blur' },
    { validator: validateIconContent, trigger: 'blur' }
  ],
  sortOrder: [
    { type: 'number', min: 0, max: 9999, message: '排序权重范围0-9999', trigger: 'blur' }
  ]
}

// 自定义验证器
async function validateIconContent(rule: any, value: string, callback: any) {
  if (!value) {
    callback(new Error('请输入图标内容'))
    return
  }

  const iconType = formData.iconType
  const validationRule = ICON_VALIDATION_RULES[iconType]
  
  if (!validationRule.pattern.test(value)) {
    callback(new Error(validationRule.message))
    return
  }

  if (validationRule.maxLength && value.length > validationRule.maxLength) {
    callback(new Error(`内容长度不能超过${validationRule.maxLength}个字符`))
    return
  }

  callback()
}

// 监听对话框显示状态
watch(() => props.visible, (newVisible) => {
  dialogVisible.value = newVisible
  if (newVisible) {
    initFormData()
  }
})

watch(dialogVisible, (newVisible) => {
  emit('update:visible', newVisible)
})

// 方法
const initFormData = () => {
  if (props.mode === 'edit' && props.formData) {
    Object.assign(formData, props.formData)
  } else {
    Object.assign(formData, {
      name: '',
      icon: '',
      iconType: 'url',
      url: '',
      sortOrder: 0,
      status: 1,
      description: ''
    })
  }
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const handleIconTypeChange = () => {
  formData.icon = ''
  nextTick(() => {
    formRef.value?.clearValidate(['icon'])
  })
}

const handleImageUpload = async (file: File) => {
  uploading.value = true
  try {
    const response = await uploadImage(file, 'url')
    formData.icon = response.url || ''
    ElMessage.success('图片上传成功')
  } catch (error) {
    ElMessage.error('图片上传失败')
  } finally {
    uploading.value = false
  }
  return false // 阻止默认上传行为
}

const handleSvgUpload = async (file: File) => {
  uploading.value = true
  try {
    const response = await uploadSvg(file)
    formData.icon = response.svgContent
    ElMessage.success('SVG文件上传成功')
  } catch (error) {
    ElMessage.error('SVG文件上传失败')
  } finally {
    uploading.value = false
  }
  return false
}

const handleBase64Upload = async (file: File) => {
  uploading.value = true
  try {
    const response = await uploadImage(file, 'base64')
    formData.icon = response.base64 || ''
    ElMessage.success('图片转换成功')
  } catch (error) {
    ElMessage.error('图片转换失败')
  } finally {
    uploading.value = false
  }
  return false
}

const showEmojiPicker = () => {
  emojiPickerVisible.value = true
}

const handleEmojiSelect = (emoji: string) => {
  formData.icon = emoji
  emojiPickerVisible.value = false
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    const submitData = { ...formData }
    emit('confirm', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.icon-type-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.option-desc {
  font-size: 12px;
  color: #909399;
}

.icon-input-container {
  width: 100%;
}

.upload-area {
  margin-top: 12px;
  text-align: center;
}

.icon-preview-container {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #c0c4cc;
  font-size: 14px;
}

.preview-placeholder .el-icon {
  font-size: 32px;
}

.dialog-footer {
  text-align: right;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #303133;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}

:deep(.el-upload) {
  width: 100%;
}

:deep(.el-upload .el-button) {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .el-row .el-col {
    margin-bottom: 16px;
  }
}
</style>
