<template>
  <div class="diamond-table-container">
    <!-- 表格工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <el-button
          v-if="selectedItems.length > 0"
          @click="selectAll"
        >
          <el-icon><Select /></el-icon>
          全选
        </el-button>
        <el-button
          v-if="selectedItems.length > 0"
          @click="clearSelection"
        >
          <el-icon><Close /></el-icon>
          取消选择
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <span class="selection-info">
          已选择 {{ selectedItems.length }} 项，共 {{ total }} 项
        </span>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="items"
      :height="tableHeight"
      stripe
      border
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    >
      <!-- 选择列 -->
      <el-table-column
        type="selection"
        width="55"
        align="center"
        fixed="left"
      />

      <!-- 序号列 -->
      <el-table-column
        type="index"
        label="序号"
        width="80"
        align="center"
        fixed="left"
        :index="getRowIndex"
      />

      <!-- 图标列 -->
      <el-table-column
        label="图标"
        width="80"
        align="center"
        fixed="left"
      >
        <template #default="{ row }">
          <DiamondIcon
            :icon="row.icon"
            :icon-type="row.iconType"
            :size="40"
            :alt="row.name"
          />
        </template>
      </el-table-column>

      <!-- 名称列 -->
      <el-table-column
        prop="name"
        label="名称"
        min-width="150"
        show-overflow-tooltip
        sortable="custom"
      >
        <template #default="{ row }">
          <div class="name-cell">
            <span class="name-text">{{ row.name }}</span>
            <el-tag
              v-if="row.status === 0"
              type="danger"
              size="small"
              class="status-tag"
            >
              禁用
            </el-tag>
          </div>
        </template>
      </el-table-column>

      <!-- 图标类型列 -->
      <el-table-column
        prop="iconType"
        label="图标类型"
        width="120"
        align="center"
        sortable="custom"
      >
        <template #default="{ row }">
          <el-tag
            :type="getIconTypeTagType(row.iconType)"
            size="small"
          >
            {{ getIconTypeLabel(row.iconType) }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 跳转链接列 -->
      <el-table-column
        prop="url"
        label="跳转链接"
        min-width="200"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <el-link
            :href="row.url"
            target="_blank"
            type="primary"
            :underline="false"
          >
            {{ row.url }}
          </el-link>
        </template>
      </el-table-column>

      <!-- 排序权重列 -->
      <el-table-column
        prop="sortOrder"
        label="排序权重"
        width="120"
        align="center"
        sortable="custom"
      />

      <!-- 状态列 -->
      <el-table-column
        prop="status"
        label="状态"
        width="100"
        align="center"
        sortable="custom"
      >
        <template #default="{ row }">
          <el-switch
            :model-value="row.status === 1"
            @change="handleStatusChange(row, $event)"
          />
        </template>
      </el-table-column>

      <!-- 创建时间列 -->
      <el-table-column
        prop="createTime"
        label="创建时间"
        width="180"
        align="center"
        sortable="custom"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ formatDateTime(row.createTime) }}
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column
        label="操作"
        width="200"
        align="center"
        fixed="right"
      >
        <template #default="{ row }">
          <el-button-group>
            <el-button
              size="small"
              type="primary"
              @click="$emit('edit', row)"
            >
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button
              size="small"
              :type="row.status === 1 ? 'warning' : 'success'"
              @click="$emit('toggle-status', row, row.status === 1 ? 0 : 1)"
            >
              <el-icon v-if="row.status === 1"><Hide /></el-icon>
              <el-icon v-else><View /></el-icon>
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="$emit('delete', row)"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElTable } from 'element-plus'
import {
  Select,
  Close,
  Edit,
  Delete,
  Hide,
  View
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'

// 组件导入
import DiamondIcon from './DiamondIcon.vue'

import type {
  DiamondPosition,
  DiamondPositionQuery,
  IconType
} from '@/types/diamond-position'
import { ICON_TYPES } from '@/types/diamond-position'

// Props
interface Props {
  items: DiamondPosition[]
  loading: boolean
  total: number
  queryParams: DiamondPositionQuery
  selected: DiamondPosition[]
}

const props = withDefaults(defineProps<Props>(), {
  items: () => [],
  loading: false,
  total: 0,
  selected: () => []
})

// Emits
const emit = defineEmits<{
  'update:selected': [value: DiamondPosition[]]
  'edit': [item: DiamondPosition]
  'delete': [item: DiamondPosition]
  'toggle-status': [item: DiamondPosition, status: 0 | 1]
  'page-change': [page: number]
  'size-change': [size: number]
  'sort-change': [sortField: string, sortOrder: string]
}>()

// 响应式数据
const tableRef = ref<InstanceType<typeof ElTable>>()
const selectedItems = ref<DiamondPosition[]>([])
const tableHeight = ref(600)

// 计算属性
const currentPage = computed({
  get: () => props.queryParams.pageNum || 1,
  set: (value) => emit('page-change', value)
})

const pageSize = computed({
  get: () => props.queryParams.pageSize || 10,
  set: (value) => emit('size-change', value)
})

// 监听选中项变化
watch(() => props.selected, (newSelected) => {
  selectedItems.value = [...newSelected]
  
  // 同步表格选中状态
  nextTick(() => {
    if (tableRef.value) {
      tableRef.value.clearSelection()
      newSelected.forEach(item => {
        const row = props.items.find(row => row.id === item.id)
        if (row) {
          tableRef.value!.toggleRowSelection(row, true)
        }
      })
    }
  })
}, { immediate: true })

watch(selectedItems, (newSelected) => {
  emit('update:selected', newSelected)
}, { deep: true })

// 方法
const getRowIndex = (index: number) => {
  return (currentPage.value - 1) * pageSize.value + index + 1
}

const getIconTypeLabel = (iconType: IconType) => {
  const type = ICON_TYPES.find(t => t.value === iconType)
  return type?.label || iconType
}

const getIconTypeTagType = (iconType: IconType) => {
  const typeMap = {
    url: 'primary',
    svg: 'success',
    base64: 'warning',
    emoji: 'info'
  }
  return typeMap[iconType] || 'default'
}

const formatDateTime = (dateTime: string | undefined) => {
  if (!dateTime) return '-'
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

const selectAll = () => {
  if (tableRef.value) {
    props.items.forEach(item => {
      tableRef.value!.toggleRowSelection(item, true)
    })
  }
}

const clearSelection = () => {
  if (tableRef.value) {
    tableRef.value.clearSelection()
  }
}

const handleSelectionChange = (selection: DiamondPosition[]) => {
  selectedItems.value = selection
}

const handleStatusChange = (row: DiamondPosition, status: boolean) => {
  emit('toggle-status', row, status ? 1 : 0)
}

const handlePageChange = (page: number) => {
  emit('page-change', page)
}

const handleSizeChange = (size: number) => {
  emit('size-change', size)
}

const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  const sortOrder = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : ''
  emit('sort-change', prop, sortOrder)
}

// 计算表格高度
const calculateTableHeight = () => {
  const windowHeight = window.innerHeight
  const headerHeight = 200 // 页面头部高度
  const toolbarHeight = 60 // 工具栏高度
  const paginationHeight = 60 // 分页高度
  const padding = 40 // 内边距
  
  tableHeight.value = windowHeight - headerHeight - toolbarHeight - paginationHeight - padding
}

// 监听窗口大小变化
window.addEventListener('resize', calculateTableHeight)
calculateTableHeight()
</script>

<style scoped>
.diamond-table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.selection-info {
  color: #606266;
  font-size: 14px;
}

.name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.name-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-tag {
  flex-shrink: 0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

/* 表格样式优化 */
:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background: #fafafa;
  color: #303133;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-table .el-button-group) {
  display: flex;
  gap: 4px;
}

:deep(.el-table .el-button-group .el-button) {
  margin-left: 0;
  border-radius: 4px;
}

:deep(.el-table .el-link) {
  font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .table-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  :deep(.el-table .el-button-group) {
    flex-direction: column;
    gap: 2px;
  }

  :deep(.el-table .el-button) {
    padding: 4px 8px;
    font-size: 12px;
  }

  .pagination-container {
    padding: 16px;
  }

  :deep(.el-pagination) {
    justify-content: center;
  }

  :deep(.el-pagination .el-pagination__sizes),
  :deep(.el-pagination .el-pagination__jump) {
    display: none;
  }
}

/* 加载状态优化 */
:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 表格行悬停效果 */
:deep(.el-table tbody tr:hover > td) {
  background-color: #f5f7fa !important;
}

/* 选中行样式 */
:deep(.el-table tbody tr.el-table__row--selected > td) {
  background-color: #ecf5ff !important;
}
</style>
