<template>
  <div class="test-container">
    <h1>金刚位功能测试页面</h1>
    
    <div class="test-section">
      <h2>1. 图标组件测试</h2>
      <div class="icon-test-grid">
        <div class="test-item">
          <h3>URL图标</h3>
          <DiamondIcon
            icon="https://via.placeholder.com/64"
            icon-type="url"
            :size="64"
            alt="URL图标测试"
          />
        </div>
        
        <div class="test-item">
          <h3>表情符号图标</h3>
          <DiamondIcon
            icon="💰"
            icon-type="emoji"
            :size="64"
            alt="表情符号测试"
          />
        </div>
        
        <div class="test-item">
          <h3>SVG图标</h3>
          <DiamondIcon
            :icon="testSvg"
            icon-type="svg"
            :size="64"
            alt="SVG图标测试"
          />
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>2. API接口测试</h2>
      <div class="api-test-buttons">
        <el-button @click="testGetList" :loading="loading">
          测试获取列表
        </el-button>
        <el-button @click="testCreateItem" :loading="loading">
          测试创建金刚位
        </el-button>
        <el-button @click="testValidateIcon" :loading="loading">
          测试图标验证
        </el-button>
      </div>
      
      <div v-if="apiResult" class="api-result">
        <h3>API测试结果：</h3>
        <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>3. 权限检查测试</h2>
      <div class="permission-test">
        <div class="permission-item">
          <span>查看权限：</span>
          <el-tag :type="DiamondPermissionChecker.canView() ? 'success' : 'danger'">
            {{ DiamondPermissionChecker.canView() ? '有权限' : '无权限' }}
          </el-tag>
        </div>
        <div class="permission-item">
          <span>新增权限：</span>
          <el-tag :type="DiamondPermissionChecker.canAdd() ? 'success' : 'danger'">
            {{ DiamondPermissionChecker.canAdd() ? '有权限' : '无权限' }}
          </el-tag>
        </div>
        <div class="permission-item">
          <span>编辑权限：</span>
          <el-tag :type="DiamondPermissionChecker.canEdit() ? 'success' : 'danger'">
            {{ DiamondPermissionChecker.canEdit() ? '有权限' : '无权限' }}
          </el-tag>
        </div>
        <div class="permission-item">
          <span>删除权限：</span>
          <el-tag :type="DiamondPermissionChecker.canDelete() ? 'success' : 'danger'">
            {{ DiamondPermissionChecker.canDelete() ? '有权限' : '无权限' }}
          </el-tag>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>4. 组合式函数测试</h2>
      <div class="composable-test">
        <el-button @click="testUseDiamondPosition">
          测试useDiamondPosition
        </el-button>
        <el-button @click="testIconValidation">
          测试图标验证
        </el-button>
        
        <div v-if="composableResult" class="composable-result">
          <h3>组合式函数测试结果：</h3>
          <pre>{{ composableResult }}</pre>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>5. 类型定义测试</h2>
      <div class="type-test">
        <div class="type-item">
          <span>图标类型选项：</span>
          <el-tag v-for="type in ICON_TYPES" :key="type.value" class="mr-2">
            {{ type.label }}
          </el-tag>
        </div>
        <div class="type-item">
          <span>设备配置：</span>
          <el-tag v-for="(config, key) in DEVICE_CONFIGS" :key="key" class="mr-2">
            {{ config.name }}
          </el-tag>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>6. 网络连接测试</h2>
      <div class="network-test">
        <el-button @click="testBackendConnection" :loading="networkTesting">
          测试后端连接
        </el-button>
        
        <div v-if="networkResult" class="network-result">
          <h3>网络测试结果：</h3>
          <el-alert
            :title="networkResult.title"
            :type="networkResult.type"
            :description="networkResult.description"
            show-icon
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

// 组件导入
import DiamondIcon from './components/DiamondIcon.vue'

// 组合式函数导入
import { useDiamondPosition } from '@/composables/useDiamondPosition'
import { useIconValidation } from '@/composables/useIconValidation'

// API导入
import { getDiamondPositionPage, addDiamondPosition } from '@/api/diamond-position'

// 类型和常量导入
import { ICON_TYPES, DEVICE_CONFIGS } from '@/types/diamond-position'
import { DiamondPermissionChecker } from '@/utils/permission'

// 响应式数据
const loading = ref(false)
const networkTesting = ref(false)
const apiResult = ref<any>(null)
const composableResult = ref<string>('')
const networkResult = ref<any>(null)

// 测试用的SVG
const testSvg = `<svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`

// 测试方法
const testGetList = async () => {
  loading.value = true
  try {
    const result = await getDiamondPositionPage({ pageNum: 1, pageSize: 10 })
    apiResult.value = result
    ElMessage.success('获取列表成功')
  } catch (error) {
    apiResult.value = { error: error.message }
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

const testCreateItem = async () => {
  loading.value = true
  try {
    const testData = {
      name: '测试金刚位',
      icon: '🔧',
      iconType: 'emoji' as const,
      url: 'https://example.com',
      sortOrder: 1,
      status: 1 as const,
      description: '这是一个测试金刚位'
    }
    
    const result = await addDiamondPosition(testData)
    apiResult.value = result
    ElMessage.success('创建金刚位成功')
  } catch (error) {
    apiResult.value = { error: error.message }
    ElMessage.error('创建金刚位失败')
  } finally {
    loading.value = false
  }
}

const testValidateIcon = async () => {
  loading.value = true
  try {
    const { validateIcon } = useIconValidation()
    const result = await validateIcon('💰', 'emoji')
    apiResult.value = { validationResult: result }
    ElMessage.success('图标验证完成')
  } catch (error) {
    apiResult.value = { error: error.message }
    ElMessage.error('图标验证失败')
  } finally {
    loading.value = false
  }
}

const testUseDiamondPosition = () => {
  const { items, loading, total, queryParams } = useDiamondPosition()
  composableResult.value = `
useDiamondPosition 测试结果：
- items: ${items.value.length} 项
- loading: ${loading.value}
- total: ${total.value}
- queryParams: ${JSON.stringify(queryParams, null, 2)}
  `
}

const testIconValidation = async () => {
  const { validateIcon, validationResult } = useIconValidation()
  await validateIcon('https://example.com/icon.png', 'url')
  composableResult.value = `
图标验证测试结果：
${JSON.stringify(validationResult.value, null, 2)}
  `
}

const testBackendConnection = async () => {
  networkTesting.value = true
  try {
    // 测试后端连接
    const response = await fetch('http://localhost:8080/api/diamond-position/page?pageNum=1&pageSize=1')
    
    if (response.ok) {
      networkResult.value = {
        title: '后端连接成功',
        type: 'success',
        description: `状态码: ${response.status}, 后端服务正常运行`
      }
    } else {
      networkResult.value = {
        title: '后端连接失败',
        type: 'error',
        description: `状态码: ${response.status}, 请检查后端服务`
      }
    }
  } catch (error) {
    networkResult.value = {
      title: '网络连接错误',
      type: 'error',
      description: `错误信息: ${error.message}, 请确保后端服务在8080端口运行`
    }
  } finally {
    networkTesting.value = false
  }
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fff;
}

.test-section h2 {
  margin: 0 0 20px 0;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}

.icon-test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.test-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.test-item h3 {
  margin: 0 0 16px 0;
  color: #606266;
}

.api-test-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.api-result,
.composable-result,
.network-result {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.api-result pre,
.composable-result pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  font-size: 12px;
  color: #303133;
}

.permission-test,
.composable-test,
.type-test,
.network-test {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.permission-item,
.type-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.mr-2 {
  margin-right: 8px;
}
</style>
