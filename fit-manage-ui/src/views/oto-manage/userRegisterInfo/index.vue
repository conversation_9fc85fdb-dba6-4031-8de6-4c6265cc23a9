<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryParams.userId" placeholder="输入用户ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="手机号" prop="encryptPhone">
              <el-input v-model="queryParams.encryptPhone" placeholder="输入加密手机号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="手机哈希" prop="phoneHash">
              <el-input v-model="queryParams.phoneHash" placeholder="输入手机哈希值" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="IP地址" prop="registerIp">
              <el-input v-model="queryParams.registerIp" placeholder="输入注册IP" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="设备信息" prop="registerDevice">
              <el-input v-model="queryParams.registerDevice" placeholder="输入设备信息" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="注册渠道" prop="registerChannel">
              <el-input v-model="queryParams.registerChannel" placeholder="输入渠道" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="邀请码" prop="inviteCode">
              <el-input v-model="queryParams.inviteCode" placeholder="输入邀请码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="注册时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeCreateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item label="加密向量" prop="encryptIv">
              <el-input v-model="queryParams.encryptIv" placeholder="输入加密向量" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="区号" prop="countryCode">
              <el-input v-model="queryParams.countryCode" placeholder="输入电话区号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="加密版本" prop="encryptionVersion">
              <el-input v-model="queryParams.encryptionVersion" placeholder="输入加密版本" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['oto-manage:userRegisterInfo:export']"
              >导出
            </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="userRegisterInfoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="注册记录ID" align="center" prop="id" v-if="true" />
        <el-table-column label="用户ID" align="center" prop="userId" />
        <el-table-column label="注册手机号" align="center" prop="encryptPhone" />
        <el-table-column label="手机号哈希" align="center" prop="phoneHash" />
        <el-table-column label="注册IP地址" align="center" prop="registerIp" />
        <el-table-column label="注册设备信息" align="center" prop="registerDevice" />
        <el-table-column label="注册渠道" align="center" prop="registerChannel" />
        <el-table-column label="注册时短信验证码" align="center" prop="smsCode" />
        <el-table-column label="邀请码" align="center" prop="inviteCode" />
        <el-table-column label="注册状态：1-成功，2-失败，3-待验证" align="center" prop="registerStatus" />
        <el-table-column label="失败原因" align="center" prop="failReason" />
        <el-table-column label="User-Agent信息" align="center" prop="userAgent" />
        <el-table-column label="注册地理位置" align="center" prop="geoLocation" />
        <el-table-column label="加密初始向量" align="center" prop="encryptIv" />
        <el-table-column label="国际电话区号" align="center" prop="countryCode" />
        <el-table-column label="加密算法版本" align="center" prop="encryptionVersion" />
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="UserRegisterInfo" lang="ts">
import { listUserRegisterInfo } from '@/api/oto-manage/userRegisterInfo';
import { UserRegisterInfoVO, UserRegisterInfoQuery } from '@/api/oto-manage/userRegisterInfo/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const userRegisterInfoList = ref<UserRegisterInfoVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const total = ref(0);
const dateRangeCreateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();

const data = reactive<PageData<UserRegisterInfoQuery, UserRegisterInfoQuery>>({
  form: {},
  rules: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: undefined,
    encryptPhone: undefined,
    phoneHash: undefined,
    registerIp: undefined,
    registerDevice: undefined,
    registerChannel: undefined,
    inviteCode: undefined,
    registerStatus: undefined,
    encryptIv: undefined,
    countryCode: undefined,
    encryptionVersion: undefined,
    params: {
      createTime: undefined
    }
  }
});

const { queryParams } = toRefs(data);

/** 查询用户注册日志（合规留痕/风控专用）列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeCreateTime.value, 'CreateTime');
  try {
    const res = await listUserRegisterInfo(queryParams.value);
    if (res?.rows && Array.isArray(res.rows)) {
      userRegisterInfoList.value = res.rows as UserRegisterInfoVO[];
      total.value = res.total || 0;
    } else {
      userRegisterInfoList.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取列表失败:', error);
    userRegisterInfoList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeCreateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: UserRegisterInfoVO[]) => {
  ids.value = selection.map((item) => item.id);
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'oto-manage/userRegisterInfo/export',
    {
      ...queryParams.value
    },
    `userRegisterInfo_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
