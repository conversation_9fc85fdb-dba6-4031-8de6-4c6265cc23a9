/**
 * 后台返回的路由动态生成name 解决缓存问题
 * 感谢 @fourteendp
 * 详见 https://github.com/vbenjs/vue-vben-admin/issues/3927
 */
import { Component, defineComponent, h } from 'vue';

interface Options {
  name?: string;
}

export function createCustomNameComponent(loader: () => Promise<any>, options: Options = {}): () => Promise<Component> {
  const { name } = options;
  let component: Component | null = null;

  // 将组件名称转换为合法的Vue组件名称格式
  // 智能处理连字符和数字，转换为驼峰命名
  const normalizeComponentName = (name?: string): string => {
    if (!name) return 'DynamicComponent';

    // 处理特殊情况：Grid-group701 -> GridGroup
    // 移除末尾的数字，将连字符分隔的单词转换为驼峰命名
    return (
      name
        .replace(/\d+$/, '') // 移除末尾的数字
        .split(/[-_\s]+/) // 按连字符、下划线、空格分割
        .filter((part) => part.length > 0) // 过滤空字符串
        .map((part) => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()) // 每个单词首字母大写
        .join('') || 'DynamicComponent' // 连接成驼峰命名
    );
  };

  const load = async () => {
    try {
      const { default: loadedComponent } = await loader();
      component = loadedComponent;
    } catch (error) {
      console.error(`Cannot resolve component ${name}, error:`, error);
    }
  };

  return async () => {
    if (!component) {
      await load();
    }

    return Promise.resolve(
      defineComponent({
        name: normalizeComponentName(name),
        render() {
          return h(component as Component);
        }
      })
    );
  };
}
