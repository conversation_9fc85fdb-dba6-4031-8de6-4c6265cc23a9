/**
 * 拖拽排序组合式函数
 * 提供HTML5拖拽API的金刚位排序功能
 */

import { ref, nextTick } from 'vue'
import type { DiamondPosition, UpdateSortOrderRequest } from '@/types/diamond-position'

/**
 * 拖拽排序组合式函数
 */
export function useDragAndDrop(
  items: Ref<DiamondPosition[]>,
  onSortChange: (newOrder: UpdateSortOrderRequest[]) => void
) {
  const draggedItem = ref<DiamondPosition | null>(null)
  const dragOverItem = ref<DiamondPosition | null>(null)
  const isDragging = ref(false)
  const draggedElement = ref<HTMLElement | null>(null)

  /**
   * 拖拽开始事件
   */
  const handleDragStart = (event: DragEvent, item: DiamondPosition) => {
    draggedItem.value = item
    isDragging.value = true
    draggedElement.value = event.target as HTMLElement

    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'move'
      event.dataTransfer.setData('text/html', '')
      
      // 设置拖拽图像
      const dragImage = createDragImage(item)
      event.dataTransfer.setDragImage(dragImage, 50, 25)
    }

    // 添加拖拽样式
    const target = event.target as HTMLElement
    const itemElement = target.closest('.diamond-item') as HTMLElement
    if (itemElement) {
      itemElement.classList.add('dragging')
    }

    // 延迟添加全局拖拽状态，避免影响拖拽开始
    nextTick(() => {
      document.body.classList.add('is-dragging')
    })
  }

  /**
   * 拖拽经过事件
   */
  const handleDragOver = (event: DragEvent) => {
    event.preventDefault()
    
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move'
    }
  }

  /**
   * 拖拽进入事件
   */
  const handleDragEnter = (event: DragEvent, item: DiamondPosition) => {
    event.preventDefault()

    if (draggedItem.value && draggedItem.value.id !== item.id) {
      dragOverItem.value = item

      const target = event.target as HTMLElement
      const itemElement = target.closest('.diamond-item') as HTMLElement
      if (itemElement) {
        // 移除其他元素的拖拽悬停样式
        document.querySelectorAll('.diamond-item.drag-over').forEach(el => {
          el.classList.remove('drag-over')
        })
        
        itemElement.classList.add('drag-over')
      }
    }
  }

  /**
   * 拖拽离开事件
   */
  const handleDragLeave = (event: DragEvent) => {
    const target = event.target as HTMLElement
    const itemElement = target.closest('.diamond-item') as HTMLElement
    
    // 检查是否真的离开了元素（而不是进入子元素）
    if (itemElement && !itemElement.contains(event.relatedTarget as Node)) {
      itemElement.classList.remove('drag-over')
    }
  }

  /**
   * 放置事件
   */
  const handleDrop = async (event: DragEvent, targetItem: DiamondPosition) => {
    event.preventDefault()

    if (!draggedItem.value || draggedItem.value.id === targetItem.id) {
      return
    }

    try {
      // 计算新的排序
      const newOrder = calculateNewOrder(draggedItem.value, targetItem)
      
      // 更新本地状态
      updateLocalOrder(newOrder)
      
      // 调用回调函数
      onSortChange(newOrder)
      
      // 显示成功提示
      showDropSuccess()
    } catch (error) {
      console.error('拖拽排序失败:', error)
      showDropError()
    } finally {
      cleanupDragState()
    }
  }

  /**
   * 拖拽结束事件
   */
  const handleDragEnd = (event: DragEvent) => {
    cleanupDragState()
  }

  /**
   * 计算新的排序顺序
   */
  const calculateNewOrder = (
    draggedItem: DiamondPosition,
    targetItem: DiamondPosition
  ): UpdateSortOrderRequest[] => {
    const currentItems = [...items.value]
    const draggedIndex = currentItems.findIndex(item => item.id === draggedItem.id)
    const targetIndex = currentItems.findIndex(item => item.id === targetItem.id)

    if (draggedIndex === -1 || targetIndex === -1) {
      throw new Error('无法找到拖拽项目或目标项目')
    }

    // 移除拖拽的项目
    const [removed] = currentItems.splice(draggedIndex, 1)

    // 插入到目标位置
    currentItems.splice(targetIndex, 0, removed)

    // 重新计算排序值
    return currentItems.map((item, index) => ({
      id: item.id!,
      sortOrder: index + 1
    }))
  }

  /**
   * 更新本地排序
   */
  const updateLocalOrder = (newOrder: UpdateSortOrderRequest[]) => {
    const orderMap = new Map(newOrder.map(item => [item.id, item.sortOrder]))

    items.value.forEach(item => {
      const newSortOrder = orderMap.get(item.id!)
      if (newSortOrder !== undefined) {
        item.sortOrder = newSortOrder
      }
    })

    // 重新排序
    items.value.sort((a, b) => a.sortOrder - b.sortOrder)
  }

  /**
   * 清理拖拽状态
   */
  const cleanupDragState = () => {
    draggedItem.value = null
    dragOverItem.value = null
    isDragging.value = false
    draggedElement.value = null

    // 移除所有拖拽相关的样式
    document.body.classList.remove('is-dragging')
    document.querySelectorAll('.diamond-item').forEach(item => {
      item.classList.remove('dragging', 'drag-over')
    })
  }

  /**
   * 创建拖拽图像
   */
  const createDragImage = (item: DiamondPosition): HTMLElement => {
    const dragImage = document.createElement('div')
    dragImage.className = 'drag-image'
    dragImage.innerHTML = `
      <div class="drag-image-content">
        <div class="drag-image-icon">
          ${renderIcon(item)}
        </div>
        <div class="drag-image-name">${item.name}</div>
      </div>
    `
    
    // 设置样式
    Object.assign(dragImage.style, {
      position: 'absolute',
      top: '-1000px',
      left: '-1000px',
      width: '100px',
      height: '80px',
      background: '#fff',
      border: '1px solid #ddd',
      borderRadius: '8px',
      padding: '8px',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      fontSize: '12px',
      textAlign: 'center',
      zIndex: '9999'
    })

    document.body.appendChild(dragImage)
    
    // 清理函数
    setTimeout(() => {
      if (document.body.contains(dragImage)) {
        document.body.removeChild(dragImage)
      }
    }, 100)

    return dragImage
  }

  /**
   * 渲染图标
   */
  const renderIcon = (item: DiamondPosition): string => {
    switch (item.iconType) {
      case 'url':
        return `<img src="${item.icon}" alt="${item.name}" style="width: 32px; height: 32px; object-fit: cover; border-radius: 4px;" />`
      case 'emoji':
        return `<span style="font-size: 24px;">${item.icon}</span>`
      case 'svg':
        return `<div style="width: 32px; height: 32px;">${item.icon}</div>`
      case 'base64':
        return `<img src="${item.icon}" alt="${item.name}" style="width: 32px; height: 32px; object-fit: cover; border-radius: 4px;" />`
      default:
        return `<div style="width: 32px; height: 32px; background: #f0f0f0; border-radius: 4px;"></div>`
    }
  }

  /**
   * 显示拖拽成功提示
   */
  const showDropSuccess = () => {
    // 创建成功提示动画
    const successTip = document.createElement('div')
    successTip.className = 'drag-success-tip'
    successTip.textContent = '排序已更新'
    
    Object.assign(successTip.style, {
      position: 'fixed',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      background: '#67c23a',
      color: '#fff',
      padding: '8px 16px',
      borderRadius: '4px',
      fontSize: '14px',
      zIndex: '10000',
      opacity: '0',
      transition: 'opacity 0.3s ease'
    })

    document.body.appendChild(successTip)

    // 显示动画
    requestAnimationFrame(() => {
      successTip.style.opacity = '1'
    })

    // 自动移除
    setTimeout(() => {
      successTip.style.opacity = '0'
      setTimeout(() => {
        if (document.body.contains(successTip)) {
          document.body.removeChild(successTip)
        }
      }, 300)
    }, 1500)
  }

  /**
   * 显示拖拽错误提示
   */
  const showDropError = () => {
    const errorTip = document.createElement('div')
    errorTip.className = 'drag-error-tip'
    errorTip.textContent = '排序更新失败'
    
    Object.assign(errorTip.style, {
      position: 'fixed',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      background: '#f56c6c',
      color: '#fff',
      padding: '8px 16px',
      borderRadius: '4px',
      fontSize: '14px',
      zIndex: '10000',
      opacity: '0',
      transition: 'opacity 0.3s ease'
    })

    document.body.appendChild(errorTip)

    requestAnimationFrame(() => {
      errorTip.style.opacity = '1'
    })

    setTimeout(() => {
      errorTip.style.opacity = '0'
      setTimeout(() => {
        if (document.body.contains(errorTip)) {
          document.body.removeChild(errorTip)
        }
      }, 300)
    }, 2000)
  }

  /**
   * 检查是否可以拖拽
   */
  const canDrag = (item: DiamondPosition): boolean => {
    return item.status === 1 // 只有启用的项目才能拖拽
  }

  /**
   * 检查是否可以放置
   */
  const canDrop = (targetItem: DiamondPosition): boolean => {
    return draggedItem.value !== null && 
           draggedItem.value.id !== targetItem.id &&
           targetItem.status === 1
  }

  return {
    // 状态
    draggedItem,
    dragOverItem,
    isDragging,
    
    // 方法
    handleDragStart,
    handleDragOver,
    handleDragEnter,
    handleDragLeave,
    handleDrop,
    handleDragEnd,
    canDrag,
    canDrop,
    cleanupDragState
  }
}

/**
 * 触摸设备拖拽支持
 */
export function useTouchDragAndDrop(
  items: Ref<DiamondPosition[]>,
  onSortChange: (newOrder: UpdateSortOrderRequest[]) => void
) {
  const touchStartPos = ref({ x: 0, y: 0 })
  const touchCurrentPos = ref({ x: 0, y: 0 })
  const touchDraggedItem = ref<DiamondPosition | null>(null)
  const isTouchDragging = ref(false)

  const handleTouchStart = (event: TouchEvent, item: DiamondPosition) => {
    const touch = event.touches[0]
    touchStartPos.value = { x: touch.clientX, y: touch.clientY }
    touchCurrentPos.value = { x: touch.clientX, y: touch.clientY }
    touchDraggedItem.value = item
  }

  const handleTouchMove = (event: TouchEvent) => {
    if (!touchDraggedItem.value) return

    event.preventDefault()
    const touch = event.touches[0]
    touchCurrentPos.value = { x: touch.clientX, y: touch.clientY }

    const deltaX = Math.abs(touch.clientX - touchStartPos.value.x)
    const deltaY = Math.abs(touch.clientY - touchStartPos.value.y)

    // 开始拖拽的阈值
    if (!isTouchDragging.value && (deltaX > 10 || deltaY > 10)) {
      isTouchDragging.value = true
      document.body.classList.add('is-touch-dragging')
    }
  }

  const handleTouchEnd = (event: TouchEvent) => {
    if (!isTouchDragging.value || !touchDraggedItem.value) {
      touchDraggedItem.value = null
      return
    }

    // 查找放置目标
    const touch = event.changedTouches[0]
    const elementBelow = document.elementFromPoint(touch.clientX, touch.clientY)
    const targetElement = elementBelow?.closest('.diamond-item') as HTMLElement
    
    if (targetElement) {
      const targetId = parseInt(targetElement.dataset.id || '0')
      const targetItem = items.value.find(item => item.id === targetId)
      
      if (targetItem && targetItem.id !== touchDraggedItem.value.id) {
        // 执行排序逻辑
        const draggedItem = touchDraggedItem.value
        const newOrder = calculateTouchOrder(draggedItem, targetItem)
        onSortChange(newOrder)
      }
    }

    // 清理状态
    isTouchDragging.value = false
    touchDraggedItem.value = null
    document.body.classList.remove('is-touch-dragging')
  }

  const calculateTouchOrder = (
    draggedItem: DiamondPosition,
    targetItem: DiamondPosition
  ): UpdateSortOrderRequest[] => {
    const currentItems = [...items.value]
    const draggedIndex = currentItems.findIndex(item => item.id === draggedItem.id)
    const targetIndex = currentItems.findIndex(item => item.id === targetItem.id)

    const [removed] = currentItems.splice(draggedIndex, 1)
    currentItems.splice(targetIndex, 0, removed)

    return currentItems.map((item, index) => ({
      id: item.id!,
      sortOrder: index + 1
    }))
  }

  return {
    isTouchDragging,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd
  }
}
