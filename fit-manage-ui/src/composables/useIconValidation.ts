import { ref, computed } from 'vue'
import type { IconType } from '@/types/diamond-position'
import { ICON_VALIDATION_RULES } from '@/types/diamond-position'

/**
 * 图标验证组合式函数
 */
export function useIconValidation() {
  const validating = ref(false)
  const validationResult = ref<{
    valid: boolean
    message: string
    type?: 'success' | 'warning' | 'error'
  }>({
    valid: false,
    message: '',
    type: 'error'
  })

  /**
   * 验证图标内容
   */
  const validateIcon = async (icon: string, iconType: IconType): Promise<boolean> => {
    validating.value = true
    
    try {
      // 基础验证
      if (!icon || !iconType) {
        validationResult.value = {
          valid: false,
          message: '图标内容和类型不能为空',
          type: 'error'
        }
        return false
      }

      const rule = ICON_VALIDATION_RULES[iconType]
      if (!rule) {
        validationResult.value = {
          valid: false,
          message: '不支持的图标类型',
          type: 'error'
        }
        return false
      }

      // 长度验证
      if (rule.maxLength && icon.length > rule.maxLength) {
        validationResult.value = {
          valid: false,
          message: `图标内容长度不能超过${rule.maxLength}个字符`,
          type: 'error'
        }
        return false
      }

      // 格式验证
      if (!rule.pattern.test(icon)) {
        validationResult.value = {
          valid: false,
          message: rule.message,
          type: 'error'
        }
        return false
      }

      // 特殊验证
      const specialValidation = await performSpecialValidation(icon, iconType)
      if (!specialValidation.valid) {
        validationResult.value = specialValidation
        return false
      }

      validationResult.value = {
        valid: true,
        message: '图标验证通过',
        type: 'success'
      }
      return true
    } catch (error) {
      validationResult.value = {
        valid: false,
        message: '图标验证失败',
        type: 'error'
      }
      return false
    } finally {
      validating.value = false
    }
  }

  /**
   * 特殊验证（针对不同图标类型的额外验证）
   */
  const performSpecialValidation = async (icon: string, iconType: IconType) => {
    switch (iconType) {
      case 'url':
        return await validateUrlIcon(icon)
      case 'svg':
        return validateSvgIcon(icon)
      case 'base64':
        return validateBase64Icon(icon)
      case 'emoji':
        return validateEmojiIcon(icon)
      default:
        return { valid: false, message: '未知的图标类型', type: 'error' as const }
    }
  }

  /**
   * 验证URL图标
   */
  const validateUrlIcon = async (url: string) => {
    try {
      // 检查URL格式
      if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('/')) {
        return {
          valid: false,
          message: 'URL必须以http://、https://或/开头',
          type: 'error' as const
        }
      }

      // 对于网络URL，可以尝试预加载验证（可选）
      if (url.startsWith('http')) {
        return new Promise<{ valid: boolean; message: string; type: 'success' | 'warning' | 'error' }>((resolve) => {
          const img = new Image()
          const timeout = setTimeout(() => {
            resolve({
              valid: true,
              message: 'URL格式正确（未验证可访问性）',
              type: 'warning'
            })
          }, 3000) // 3秒超时

          img.onload = () => {
            clearTimeout(timeout)
            resolve({
              valid: true,
              message: 'URL图标验证通过',
              type: 'success'
            })
          }

          img.onerror = () => {
            clearTimeout(timeout)
            resolve({
              valid: false,
              message: 'URL图标无法加载',
              type: 'error'
            })
          }

          img.src = url
        })
      }

      return {
        valid: true,
        message: 'URL格式正确',
        type: 'success' as const
      }
    } catch (error) {
      return {
        valid: false,
        message: 'URL验证失败',
        type: 'error' as const
      }
    }
  }

  /**
   * 验证SVG图标
   */
  const validateSvgIcon = (svg: string) => {
    try {
      // 基础SVG格式检查
      if (!svg.includes('<svg') || !svg.includes('</svg>')) {
        return {
          valid: false,
          message: 'SVG格式不正确，必须包含<svg>标签',
          type: 'error' as const
        }
      }

      // 检查是否包含危险内容
      const dangerousPatterns = [
        /<script[\s\S]*?<\/script>/gi,
        /on\w+\s*=/gi,
        /javascript:/gi,
        /<iframe/gi,
        /<object/gi,
        /<embed/gi
      ]

      for (const pattern of dangerousPatterns) {
        if (pattern.test(svg)) {
          return {
            valid: false,
            message: 'SVG包含不安全的内容',
            type: 'error' as const
          }
        }
      }

      // 尝试解析SVG
      const parser = new DOMParser()
      const doc = parser.parseFromString(svg, 'image/svg+xml')
      const parseError = doc.querySelector('parsererror')
      
      if (parseError) {
        return {
          valid: false,
          message: 'SVG格式解析失败',
          type: 'error' as const
        }
      }

      return {
        valid: true,
        message: 'SVG验证通过',
        type: 'success' as const
      }
    } catch (error) {
      return {
        valid: false,
        message: 'SVG验证失败',
        type: 'error' as const
      }
    }
  }

  /**
   * 验证Base64图标
   */
  const validateBase64Icon = (base64: string) => {
    try {
      // 检查Base64格式
      if (!base64.startsWith('data:image/')) {
        return {
          valid: false,
          message: 'Base64必须以data:image/开头',
          type: 'error' as const
        }
      }

      // 检查是否包含base64标识
      if (!base64.includes('base64,')) {
        return {
          valid: false,
          message: 'Base64格式不正确，缺少base64标识',
          type: 'error' as const
        }
      }

      // 提取base64数据部分
      const base64Data = base64.split('base64,')[1]
      if (!base64Data) {
        return {
          valid: false,
          message: 'Base64数据为空',
          type: 'error' as const
        }
      }

      // 验证base64编码格式
      const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/
      if (!base64Pattern.test(base64Data)) {
        return {
          valid: false,
          message: 'Base64编码格式不正确',
          type: 'error' as const
        }
      }

      // 检查数据长度（避免过大的图片）
      if (base64Data.length > 100000) { // 约75KB
        return {
          valid: false,
          message: 'Base64图片过大，建议使用URL方式',
          type: 'warning' as const
        }
      }

      return {
        valid: true,
        message: 'Base64验证通过',
        type: 'success' as const
      }
    } catch (error) {
      return {
        valid: false,
        message: 'Base64验证失败',
        type: 'error' as const
      }
    }
  }

  /**
   * 验证表情符号图标
   */
  const validateEmojiIcon = (emoji: string) => {
    try {
      // 长度检查
      if (emoji.length === 0) {
        return {
          valid: false,
          message: '表情符号不能为空',
          type: 'error' as const
        }
      }

      if (emoji.length > 10) {
        return {
          valid: false,
          message: '表情符号长度不能超过10个字符',
          type: 'error' as const
        }
      }

      // 检查是否包含有效的Unicode字符
      const emojiPattern = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u
      
      if (!emojiPattern.test(emoji)) {
        return {
          valid: true, // 允许普通字符作为图标
          message: '建议使用表情符号字符',
          type: 'warning' as const
        }
      }

      return {
        valid: true,
        message: '表情符号验证通过',
        type: 'success' as const
      }
    } catch (error) {
      return {
        valid: false,
        message: '表情符号验证失败',
        type: 'error' as const
      }
    }
  }

  /**
   * 快速验证（仅格式检查，不进行网络请求）
   */
  const quickValidate = (icon: string, iconType: IconType): boolean => {
    if (!icon || !iconType) return false
    
    const rule = ICON_VALIDATION_RULES[iconType]
    if (!rule) return false
    
    if (rule.maxLength && icon.length > rule.maxLength) return false
    
    return rule.pattern.test(icon)
  }

  /**
   * 获取图标类型的验证规则
   */
  const getValidationRule = (iconType: IconType) => {
    return ICON_VALIDATION_RULES[iconType]
  }

  return {
    validating: computed(() => validating.value),
    validationResult: computed(() => validationResult.value),
    validateIcon,
    quickValidate,
    getValidationRule
  }
}
