/**
 * 金刚位配置管理组合式函数
 * 提供完整的金刚位管理功能
 */

import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { diamondPositionController } from '@/api/diamond-position/controller'
import type {
  DiamondPosition,
  DiamondPositionQuery,
  CreateDiamondPositionRequest,
  UpdateDiamondPositionRequest,
  UpdateSortOrderRequest,
  IconType,
  PlatformType,
  DeviceType,
  IconValidationResult
} from '@/types/diamond-position'

/**
 * 金刚位管理组合式函数
 */
export function useDiamondPosition() {
  // 响应式数据
  const items = ref<DiamondPosition[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const total = ref(0)

  // 查询参数
  const queryParams = reactive<DiamondPositionQuery>({
    pageNum: 1,
    pageSize: 20,
    name: '',
    iconType: undefined,
    platform: undefined,
    status: undefined,
    groupId: undefined
  })

  // 计算属性
  const enabledItems = computed(() => 
    items.value.filter(item => item.status === 1)
  )

  const sortedItems = computed(() => 
    [...items.value].sort((a, b) => a.sortOrder - b.sortOrder)
  )

  /**
   * 获取金刚位列表
   */
  const fetchItems = async (query?: Partial<DiamondPositionQuery>) => {
    loading.value = true
    error.value = null

    try {
      const searchParams = { ...queryParams, ...query }
      const response = await diamondPositionController.getList(searchParams)
      
      if (response.success) {
        items.value = response.data.data
        total.value = response.data.total
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取列表失败'
      console.error('获取金刚位列表失败:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取启用的金刚位列表
   */
  const fetchEnabledItems = async (platform?: PlatformType) => {
    loading.value = true
    error.value = null

    try {
      const response = await diamondPositionController.getEnabledList(platform)
      
      if (response.success) {
        items.value = response.data
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取启用列表失败'
      console.error('获取启用金刚位列表失败:', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 创建金刚位
   */
  const createItem = async (data: CreateDiamondPositionRequest) => {
    loading.value = true
    error.value = null

    try {
      const response = await diamondPositionController.create(data)
      
      if (response.success) {
        items.value.push(response.data)
        ElMessage.success('创建成功')
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建失败'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新金刚位
   */
  const updateItem = async (id: number, data: UpdateDiamondPositionRequest) => {
    loading.value = true
    error.value = null

    try {
      const response = await diamondPositionController.update(id, data)
      
      if (response.success) {
        const index = items.value.findIndex(item => item.id === id)
        if (index !== -1) {
          items.value[index] = response.data
        }
        ElMessage.success('更新成功')
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新失败'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 删除金刚位
   */
  const deleteItem = async (id: number) => {
    loading.value = true
    error.value = null

    try {
      const response = await diamondPositionController.delete(id)
      
      if (response.success) {
        items.value = items.value.filter(item => item.id !== id)
        ElMessage.success('删除成功')
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除失败'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 批量删除金刚位
   */
  const batchDelete = async (ids: number[]) => {
    loading.value = true
    error.value = null

    try {
      const response = await diamondPositionController.batchDelete(ids)
      
      if (response.success) {
        items.value = items.value.filter(item => !ids.includes(item.id!))
        ElMessage.success(`批量删除成功，共删除${ids.length}项`)
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '批量删除失败'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 切换状态
   */
  const toggleStatus = async (id: number, status: 0 | 1) => {
    loading.value = true
    error.value = null

    try {
      const response = await diamondPositionController.toggleStatus(id, status)
      
      if (response.success) {
        const index = items.value.findIndex(item => item.id === id)
        if (index !== -1) {
          items.value[index].status = status
        }
        ElMessage.success(`${status ? '启用' : '禁用'}成功`)
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '状态切换失败'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 批量更新状态
   */
  const batchUpdateStatus = async (ids: number[], status: 0 | 1) => {
    loading.value = true
    error.value = null

    try {
      const response = await diamondPositionController.batchUpdateStatus(ids, status)
      
      if (response.success) {
        items.value.forEach(item => {
          if (ids.includes(item.id!)) {
            item.status = status
          }
        })
        ElMessage.success(`批量${status ? '启用' : '禁用'}成功`)
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '批量状态更新失败'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新排序
   */
  const updateSortOrder = async (sortItems: UpdateSortOrderRequest[]) => {
    loading.value = true
    error.value = null

    try {
      const response = await diamondPositionController.updateSortOrder(sortItems)
      
      if (response.success) {
        // 更新本地排序
        const sortMap = new Map(sortItems.map(item => [item.id, item.sortOrder]))
        items.value.forEach(item => {
          const newSortOrder = sortMap.get(item.id!)
          if (newSortOrder !== undefined) {
            item.sortOrder = newSortOrder
          }
        })
        
        // 重新排序
        items.value.sort((a, b) => a.sortOrder - b.sortOrder)
        ElMessage.success('排序更新成功')
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '排序更新失败'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 重置查询参数
   */
  const resetQuery = () => {
    Object.assign(queryParams, {
      pageNum: 1,
      pageSize: 20,
      name: '',
      iconType: undefined,
      platform: undefined,
      status: undefined,
      groupId: undefined
    })
  }

  /**
   * 搜索
   */
  const search = async () => {
    queryParams.pageNum = 1
    await fetchItems()
  }

  /**
   * 重置搜索
   */
  const resetSearch = async () => {
    resetQuery()
    await fetchItems()
  }

  return {
    // 状态
    items,
    loading,
    error,
    total,
    queryParams,
    
    // 计算属性
    enabledItems,
    sortedItems,
    
    // 方法
    fetchItems,
    fetchEnabledItems,
    createItem,
    updateItem,
    deleteItem,
    batchDelete,
    toggleStatus,
    batchUpdateStatus,
    updateSortOrder,
    resetQuery,
    search,
    resetSearch
  }
}

/**
 * 图标处理组合式函数
 */
export function useIconHandler() {
  const uploading = ref(false)
  const validating = ref(false)

  /**
   * 上传图片
   */
  const uploadImage = async (file: File, targetType: 'url' | 'base64') => {
    uploading.value = true
    
    try {
      const response = await diamondPositionController.uploadImage(file, targetType)
      
      if (response.success) {
        ElMessage.success('上传成功')
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : '上传失败'
      ElMessage.error(message)
      throw err
    } finally {
      uploading.value = false
    }
  }

  /**
   * 上传SVG
   */
  const uploadSvg = async (file: File) => {
    uploading.value = true
    
    try {
      const response = await diamondPositionController.uploadSvg(file)
      
      if (response.success) {
        ElMessage.success('SVG文件读取成功')
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : 'SVG上传失败'
      ElMessage.error(message)
      throw err
    } finally {
      uploading.value = false
    }
  }

  /**
   * 验证图标
   */
  const validateIcon = async (icon: string, iconType: IconType): Promise<IconValidationResult> => {
    validating.value = true
    
    try {
      const response = await diamondPositionController.validateIcon(icon, iconType)
      
      if (response.success) {
        return response.data
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      console.error('图标验证失败:', err)
      return {
        valid: false,
        message: err instanceof Error ? err.message : '验证失败'
      }
    } finally {
      validating.value = false
    }
  }

  /**
   * 检测图标类型
   */
  const detectIconType = async (icon: string): Promise<IconType> => {
    try {
      const response = await diamondPositionController.detectIconType(icon)
      
      if (response.success) {
        return response.data.iconType
      } else {
        throw new Error(response.message)
      }
    } catch (err) {
      console.error('图标类型检测失败:', err)
      return 'url' // 默认返回url类型
    }
  }

  return {
    uploading,
    validating,
    uploadImage,
    uploadSvg,
    validateIcon,
    detectIconType
  }
}
