import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  listGridItem,
  addGridItem,
  updateGridItem,
  delGridItem,
  updateGridItemStatus,
  copyGridItem,
  listGridGroup,
  addGridGroup,
  updateGridGroup,
  delGridGroup,
  updateGridGroupStatus
} from '@/api/otoconfig/gridconfig';
import type { GridItemQuery, GridItemVO, GridItemForm, GridGroupVO, GridPreviewVO, TableDataInfo } from '@/api/otoconfig/gridconfig/types';

/**
 * 金刚位配置状态管理
 * 提供全局状态管理、数据同步等功能
 */
export const useGridConfigStore = defineStore('gridConfig', () => {
  // ==================== 状态定义 ====================

  // 金刚位列表数据
  const gridItems = ref<GridItemVO[]>([]);
  const gridItemsTotal = ref(0);
  const gridItemsLoading = ref(false);

  // 分组数据
  const gridGroups = ref<GridGroupVO[]>([]);
  const gridGroupsLoading = ref(false);

  // 预览数据
  const previewData = ref<GridPreviewVO | null>(null);
  const previewLoading = ref(false);

  // 当前查询参数
  const currentQuery = ref<GridItemQuery>({
    pageNum: 1,
    pageSize: 10,
    title: '',
    groupId: undefined,
    platform: '',
    isActive: ''
  });

  // 选中的项目
  const selectedItems = ref<GridItemVO[]>([]);
  const selectedIds = ref<number[]>([]);

  // ==================== 计算属性 ====================

  /**
   * 分组选项（用于下拉框）
   */
  const groupOptions = computed(() => [
    { label: '全部分组', value: '' },
    ...gridGroups.value.map((group) => ({
      label: group.groupName,
      value: group.id
    }))
  ]);

  /**
   * 启用的金刚位数量
   */
  const enabledItemsCount = computed(() => gridItems.value.filter((item) => item.isActive === '0').length);

  /**
   * 禁用的金刚位数量
   */
  const disabledItemsCount = computed(() => gridItems.value.filter((item) => item.isActive === '1').length);

  /**
   * 按分组统计
   */
  const itemsByGroup = computed(() => {
    const result: Record<string, GridItemVO[]> = {};
    gridItems.value.forEach((item) => {
      const groupName = gridGroups.value.find((g) => g.id === item.groupId)?.groupName || '未分组';
      if (!result[groupName]) {
        result[groupName] = [];
      }
      result[groupName].push(item);
    });
    return result;
  });

  /**
   * 按平台统计
   */
  const itemsByPlatform = computed(() => {
    const result: Record<string, number> = {
      ALL: 0,
      IOS: 0,
      ANDROID: 0,
      H5: 0
    };

    gridItems.value.forEach((item) => {
      if (result.hasOwnProperty(item.platform)) {
        result[item.platform]++;
      }
    });

    return result;
  });

  /**
   * 是否有选中项目
   */
  const hasSelectedItems = computed(() => selectedItems.value.length > 0);

  /**
   * 选中项目的状态统计
   */
  const selectedItemsStats = computed(() => {
    const enabled = selectedItems.value.filter((item) => item.isActive === '0').length;
    const disabled = selectedItems.value.filter((item) => item.isActive === '1').length;
    return { enabled, disabled, total: selectedItems.value.length };
  });

  // ==================== Actions ====================

  /**
   * 获取金刚位列表
   */
  const fetchGridItems = async (query: GridItemQuery) => {
    try {
      gridItemsLoading.value = true;

      // 更新当前查询参数
      currentQuery.value = { ...query };

      const response = await listGridItem(query);

      if (response.code === 200) {
        gridItems.value = response.rows || [];
        gridItemsTotal.value = response.total || 0;
        return { success: true, data: response };
      } else {
        ElMessage.error(response.msg || '获取数据失败');
        return { success: false, error: response.msg };
      }
    } catch (error) {
      console.error('获取金刚位列表失败:', error);
      ElMessage.error('获取数据失败，请稍后重试');
      return { success: false, error };
    } finally {
      gridItemsLoading.value = false;
    }
  };

  /**
   * 获取分组列表
   */
  const fetchGridGroups = async () => {
    try {
      gridGroupsLoading.value = true;

      const response = await listGridGroup({ pageNum: 1, pageSize: 100 });

      if (response.code === 200) {
        gridGroups.value = (response.rows || []) as GridGroupVO[];
        return { success: true, data: response.rows };
      } else {
        ElMessage.error(response.msg || '获取分组数据失败');
        return { success: false, error: response.msg };
      }
    } catch (error) {
      console.error('获取分组列表失败:', error);
      return { success: false, error };
    } finally {
      gridGroupsLoading.value = false;
    }
  };

  /**
   * 获取预览数据
   */
  const fetchPreviewData = async (params: { platform?: string; groupId?: number }) => {
    try {
      previewLoading.value = true;

      const response = await listGridItem({
        ...params,
        pageNum: 1,
        pageSize: 1000
      });

      if (response.code === 200) {
        previewData.value = response.data as unknown as GridPreviewVO;
        return { success: true, data: response.data };
      } else {
        ElMessage.error(response.msg || '获取预览数据失败');
        return { success: false, error: response.msg };
      }
    } catch (error) {
      console.error('获取预览数据失败:', error);
      return { success: false, error };
    } finally {
      previewLoading.value = false;
    }
  };

  /**
   * 添加金刚位
   */
  const addGridItemAction = async (formData: GridItemForm) => {
    try {
      const response = await addGridItem(formData);

      if (response.code === 200) {
        ElMessage.success('添加成功');
        // 刷新列表
        await fetchGridItems(currentQuery.value);
        return { success: true, data: response.data };
      } else {
        ElMessage.error(response.msg || '添加失败');
        return { success: false, error: response.msg };
      }
    } catch (error) {
      console.error('添加金刚位失败:', error);
      ElMessage.error('添加失败，请稍后重试');
      return { success: false, error };
    }
  };

  /**
   * 更新金刚位
   */
  const updateGridItemAction = async (formData: GridItemForm) => {
    try {
      const response = await updateGridItem(formData);

      if (response.code === 200) {
        ElMessage.success('更新成功');
        // 更新本地数据
        const index = gridItems.value.findIndex((item) => item.id === formData.id);
        if (index !== -1) {
          // 合并更新数据
          Object.assign(gridItems.value[index], formData);
        }
        return { success: true, data: response.data };
      } else {
        ElMessage.error(response.msg || '更新失败');
        return { success: false, error: response.msg };
      }
    } catch (error) {
      console.error('更新金刚位失败:', error);
      ElMessage.error('更新失败，请稍后重试');
      return { success: false, error };
    }
  };

  /**
   * 删除金刚位
   */
  const deleteGridItemAction = async (id: number) => {
    try {
      const response = await delGridItem(id);

      if (response.code === 200) {
        ElMessage.success('删除成功');
        // 从本地数据中移除
        const index = gridItems.value.findIndex((item) => item.id === id);
        if (index !== -1) {
          gridItems.value.splice(index, 1);
          gridItemsTotal.value--;
        }
        return { success: true };
      } else {
        ElMessage.error(response.msg || '删除失败');
        return { success: false, error: response.msg };
      }
    } catch (error) {
      console.error('删除金刚位失败:', error);
      ElMessage.error('删除失败，请稍后重试');
      return { success: false, error };
    }
  };

  /**
   * 设置选中项目
   */
  const setSelectedItems = (items: GridItemVO[]) => {
    selectedItems.value = items;
    selectedIds.value = items.map((item) => Number(item.id)).filter((id) => !isNaN(id));
  };

  /**
   * 清空选中项目
   */
  const clearSelectedItems = () => {
    selectedItems.value = [];
    selectedIds.value = [];
  };

  /**
   * 根据ID获取金刚位
   */
  const getGridItemById = (id: number): GridItemVO | undefined => {
    return gridItems.value.find((item) => item.id === id);
  };

  /**
   * 根据分组ID获取金刚位列表
   */
  const getGridItemsByGroupId = (groupId: number): GridItemVO[] => {
    return gridItems.value.filter((item) => item.groupId === groupId);
  };

  /**
   * 根据平台获取金刚位列表
   */
  const getGridItemsByPlatform = (platform: string): GridItemVO[] => {
    return gridItems.value.filter((item) => item.platform === platform || item.platform === 'ALL');
  };



  /**
   * 重置状态
   */
  const resetState = () => {
    gridItems.value = [];
    gridItemsTotal.value = 0;
    gridGroups.value = [];
    previewData.value = null;
    selectedItems.value = [];
    selectedIds.value = [];
    currentQuery.value = {
      pageNum: 1,
      pageSize: 10,
      title: '',
      groupId: undefined,
      platform: '',
      isActive: ''
    };
  };

  /**
   * 初始化数据
   */
  const initializeData = async () => {
    try {
      // 并行获取分组和金刚位数据
      const [groupsResult, itemsResult] = await Promise.all([fetchGridGroups(), fetchGridItems(currentQuery.value)]);

      return {
        success: groupsResult.success && itemsResult.success,
        groups: groupsResult.data,
        items: itemsResult.data
      };
    } catch (error) {
      console.error('初始化数据失败:', error);
      return { success: false, error };
    }
  };

  return {
    // 状态
    gridItems,
    gridItemsTotal,
    gridItemsLoading,
    gridGroups,
    gridGroupsLoading,
    previewData,
    previewLoading,
    currentQuery,
    selectedItems,
    selectedIds,

    // 计算属性
    groupOptions,
    enabledItemsCount,
    disabledItemsCount,
    itemsByGroup,
    itemsByPlatform,
    hasSelectedItems,
    selectedItemsStats,

    // 方法
    fetchGridItems,
    fetchGridGroups,
    fetchPreviewData,
    addGridItem: addGridItemAction,
    updateGridItem: updateGridItemAction,
    deleteGridItem: deleteGridItemAction,
    setSelectedItems,
    clearSelectedItems,
    getGridItemById,
    getGridItemsByGroupId,
    getGridItemsByPlatform,
    resetState,
    initializeData
  };
});
