{"$schema": "https://json.schemastore.org/package", "name": "ruoyi-vue-plus", "version": "5.4.0-2.4.0", "description": "RuoYi-Vue-Plus多租户管理系统", "author": "LionLi", "license": "MIT", "type": "module", "scripts": {"dev": "vite serve --mode development", "build:prod": "vite build --mode production", "build:dev": "vite build --mode development", "preview": "vite preview", "lint:eslint": "eslint", "lint:eslint:fix": "eslint --fix", "prettier": "prettier --write ."}, "repository": {"type": "git", "url": "https://gitee.com/JavaLionLi/plus-ui.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@highlightjs/vue-plugin": "2.1.0", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "13.1.0", "animate.css": "4.1.1", "await-to-js": "3.0.0", "axios": "1.8.4", "crypto-js": "4.2.0", "echarts": "5.6.0", "element-plus": "2.9.8", "file-saver": "2.0.5", "highlight.js": "11.9.0", "image-conversion": "2.1.1", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "nprogress": "0.2.0", "pinia": "3.0.2", "screenfull": "6.0.2", "vue": "3.5.13", "vue-cropper": "1.1.1", "vue-i18n": "11.1.3", "vue-json-pretty": "2.4.0", "vue-router": "4.5.0", "vue-types": "6.0.0", "vxe-table": "4.13.7"}, "devDependencies": {"@iconify/json": "^2.2.276", "@types/crypto-js": "4.2.2", "@types/file-saver": "2.0.7", "@types/js-cookie": "3.0.6", "@types/node": "^22.13.4", "@types/nprogress": "0.2.3", "@unocss/preset-attributify": "66.0.0", "@unocss/preset-icons": "66.0.0", "@unocss/preset-uno": "66.0.0", "@vitejs/plugin-vue": "5.2.3", "@vue/compiler-sfc": "3.5.13", "@vue/eslint-config-prettier": "10.2.0", "@vue/eslint-config-typescript": "14.4.0", "autoprefixer": "10.4.20", "eslint": "9.21.0", "eslint-plugin-prettier": "5.2.3", "eslint-plugin-vue": "9.32.0", "globals": "16.0.0", "playwright": "^1.55.0", "prettier": "3.5.2", "sass": "1.87.0", "typescript": "~5.8.3", "unocss": "66.0.0", "unplugin-auto-import": "19.1.2", "unplugin-icons": "22.1.0", "unplugin-vue-components": "28.5.0", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "6.3.2", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons-ng": "^1.4.0", "vite-plugin-vue-devtools": "7.7.5", "vitest": "3.1.2", "vue-tsc": "^2.2.8"}, "overrides": {"quill": "2.0.2"}, "engines": {"node": ">=18.18.0", "npm": ">=8.9.0"}, "browserslist": ["Chrome >= 87", "Edge >= 88", "Safari >= 14", "Firefox >= 78"]}