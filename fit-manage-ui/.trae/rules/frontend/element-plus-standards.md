# Fit-Manage-UI Element Plus开发规范

> **适用范围**：RuoYi-Vue-Plus管理后台前端开发
> **技术栈**：Vue3 + TypeScript + Element Plus + Vite

---

## 🎨 Element Plus组件使用规范

### 表单组件规范

#### 基础表单结构
```vue
<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    label-width="120px"
    class="form-container"
  >
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="formData.username"
            placeholder="请输入用户名"
            clearable
            maxlength="20"
            show-word-limit
          />
        </el-form-item>
      </el-col>
      
      <el-col :span="12">
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="formData.email"
            type="email"
            placeholder="请输入邮箱地址"
            clearable
          />
        </el-form-item>
      </el-col>
    </el-row>
    
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="角色" prop="roleIds">
          <el-select
            v-model="formData.roleIds"
            multiple
            placeholder="请选择角色"
            style="width: 100%"
          >
            <el-option
              v-for="role in roleOptions"
              :key="role.value"
              :label="role.label"
              :value="role.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      
      <el-col :span="12">
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
    
    <el-form-item label="备注" prop="remark">
      <el-input
        v-model="formData.remark"
        type="textarea"
        :rows="3"
        placeholder="请输入备注信息"
        maxlength="500"
        show-word-limit
      />
    </el-form-item>
    
    <el-form-item>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ isEdit ? '更新' : '创建' }}
      </el-button>
      <el-button @click="handleReset">重置</el-button>
      <el-button @click="handleCancel">取消</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'

interface UserForm {
  id?: number
  username: string
  email: string
  roleIds: number[]
  status: number
  remark: string
}

const props = defineProps<{
  modelValue?: UserForm
  isEdit?: boolean
}>()

const emit = defineEmits<{
  submit: [data: UserForm]
  cancel: []
}>()

const formRef = ref<FormInstance>()
const loading = ref(false)
const roleOptions = ref([
  { label: '管理员', value: 1 },
  { label: '普通用户', value: 2 }
])

const formData = reactive<UserForm>({
  username: '',
  email: '',
  roleIds: [],
  status: 1,
  remark: ''
})

const formRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/, message: '用户名只能包含字母、数字、下划线和中文', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  roleIds: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    emit('submit', { ...formData })
  } catch (error) {
    ElMessage.error('表单验证失败，请检查输入内容')
  } finally {
    loading.value = false
  }
}

const handleReset = () => {
  formRef.value?.resetFields()
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.form-container {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  
  .el-form-item {
    margin-bottom: 20px;
  }
}
</style>
```

### 表格组件规范

#### 数据表格结构
```vue
<template>
  <div class="table-container">
    <!-- 搜索区域 -->
    <div class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户名">
          <el-input
            v-model="searchForm.username"
            placeholder="请输入用户名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="正常" :value="1" />
            <el-option label="停用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 操作区域 -->
    <div class="table-header">
      <div class="header-left">
        <el-button type="primary" @click="handleAdd" v-hasPermi="['user:add']">
          <el-icon><Plus /></el-icon>
          新增用户
        </el-button>
        <el-button 
          type="danger" 
          @click="handleBatchDelete" 
          :disabled="!selectedRows.length"
          v-hasPermi="['user:delete']"
        >
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
        <el-button type="info" @click="handleExport" v-hasPermi="['user:export']">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
      <div class="header-right">
        <el-tooltip content="刷新" placement="top">
          <el-button circle @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="密度" placement="top">
          <el-dropdown @command="handleSizeChange">
            <el-button circle>
              <el-icon><Operation /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="large">宽松</el-dropdown-item>
                <el-dropdown-item command="default">默认</el-dropdown-item>
                <el-dropdown-item command="small">紧凑</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-tooltip>
      </div>
    </div>
    
    <!-- 数据表格 -->
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="tableData"
      :size="tableSize"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      stripe
      border
      style="width: 100%"
    >
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="序号" type="index" width="60" align="center" />
      
      <el-table-column 
        label="用户名" 
        prop="username" 
        sortable="custom"
        show-overflow-tooltip
        min-width="120"
      />
      
      <el-table-column 
        label="邮箱" 
        prop="email" 
        show-overflow-tooltip
        min-width="180"
      />
      
      <el-table-column label="角色" prop="roles" min-width="120">
        <template #default="{ row }">
          <el-tag
            v-for="role in row.roles"
            :key="role.id"
            type="info"
            size="small"
            style="margin-right: 5px"
          >
            {{ role.name }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="状态" prop="status" width="100" align="center">
        <template #default="{ row }">
          <el-switch
            v-model="row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(row)"
            v-hasPermi="['user:edit']"
          />
        </template>
      </el-table-column>
      
      <el-table-column label="创建时间" prop="createTime" width="180" align="center">
        <template #default="{ row }">
          <span>{{ formatDate(row.createTime) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template #default="{ row }">
          <el-button 
            type="primary" 
            size="small" 
            @click="handleEdit(row)"
            v-hasPermi="['user:edit']"
          >
            编辑
          </el-button>
          <el-button 
            type="danger" 
            size="small" 
            @click="handleDelete(row)"
            v-hasPermi="['user:delete']"
          >
            删除
          </el-button>
          <el-dropdown @command="(command) => handleCommand(command, row)">
            <el-button type="info" size="small">
              更多<el-icon><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="resetPassword" v-hasPermi="['user:resetPwd']">
                  重置密码
                </el-dropdown-item>
                <el-dropdown-item command="viewDetail">
                  查看详情
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { TableInstance } from 'element-plus'
import { Search, Refresh, Plus, Delete, Download, Operation, ArrowDown } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/date'
import { getUserList, deleteUser, updateUserStatus } from '@/api/user'

interface User {
  id: number
  username: string
  email: string
  roles: Array<{ id: number; name: string }>
  status: number
  createTime: string
}

const tableRef = ref<TableInstance>()
const loading = ref(false)
const tableData = ref<User[]>([])
const selectedRows = ref<User[]>([])
const tableSize = ref<'large' | 'default' | 'small'>('default')

const searchForm = reactive({
  username: '',
  status: undefined
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 获取表格数据
const getTableData = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      page: pagination.page,
      size: pagination.size
    }
    const response = await getUserList(params)
    tableData.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getTableData()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    username: '',
    status: undefined
  })
  handleSearch()
}

// 刷新
const handleRefresh = () => {
  getTableData()
}

// 新增
const handleAdd = () => {
  // 打开新增对话框
}

// 编辑
const handleEdit = (row: User) => {
  // 打开编辑对话框
}

// 删除
const handleDelete = async (row: User) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${row.username}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteUser(row.id)
    ElMessage.success('删除成功')
    getTableData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  const ids = selectedRows.value.map(row => row.id)
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${ids.length} 个用户吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用批量删除API
    ElMessage.success('批量删除成功')
    getTableData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 状态切换
const handleStatusChange = async (row: User) => {
  try {
    await updateUserStatus(row.id, row.status)
    ElMessage.success('状态更新成功')
  } catch (error) {
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1
    ElMessage.error('状态更新失败')
  }
}

// 选择变化
const handleSelectionChange = (selection: User[]) => {
  selectedRows.value = selection
}

// 排序变化
const handleSortChange = ({ prop, order }: any) => {
  // 处理排序逻辑
  getTableData()
}

// 表格密度变化
const handleSizeChange = (size: 'large' | 'default' | 'small') => {
  tableSize.value = size
}

// 分页大小变化
const handleSizeChange = () => {
  pagination.page = 1
  getTableData()
}

// 当前页变化
const handleCurrentChange = () => {
  getTableData()
}

// 更多操作
const handleCommand = (command: string, row: User) => {
  switch (command) {
    case 'resetPassword':
      // 重置密码逻辑
      break
    case 'viewDetail':
      // 查看详情逻辑
      break
  }
}

// 导出
const handleExport = () => {
  // 导出逻辑
}

onMounted(() => {
  getTableData()
})
</script>

<style lang="scss" scoped>
.table-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  
  .search-form {
    margin-bottom: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;
  }
  
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      display: flex;
      gap: 10px;
    }
    
    .header-right {
      display: flex;
      gap: 10px;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
```

---

## 🎛️ 布局组件规范

### 页面布局结构
```vue
<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item>系统管理</el-breadcrumb-item>
          <el-breadcrumb-item>用户管理</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="header-right">
        <el-button type="text" @click="handleHelp">
          <el-icon><QuestionFilled /></el-icon>
          帮助
        </el-button>
      </div>
    </div>
    
    <!-- 页面内容 -->
    <div class="page-content">
      <el-card shadow="never">
        <!-- 内容区域 -->
        <slot />
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { QuestionFilled } from '@element-plus/icons-vue'

const handleHelp = () => {
  // 打开帮助文档
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 20px;
    height: 50px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  }
  
  .page-content {
    .el-card {
      border-radius: 8px;
    }
  }
}
</style>
```

---

## 🎨 主题定制规范

### CSS变量定义
```scss
// styles/element-variables.scss
:root {
  // 主色调
  --el-color-primary: #409eff;
  --el-color-primary-light-3: #79bbff;
  --el-color-primary-light-5: #a0cfff;
  --el-color-primary-light-7: #c6e2ff;
  --el-color-primary-light-8: #d9ecff;
  --el-color-primary-light-9: #ecf5ff;
  --el-color-primary-dark-2: #337ecc;
  
  // 成功色
  --el-color-success: #67c23a;
  --el-color-success-light-3: #95d475;
  --el-color-success-light-5: #b3e19d;
  --el-color-success-light-7: #d1edc4;
  --el-color-success-light-8: #e1f3d8;
  --el-color-success-light-9: #f0f9eb;
  --el-color-success-dark-2: #529b2e;
  
  // 警告色
  --el-color-warning: #e6a23c;
  --el-color-warning-light-3: #eebe77;
  --el-color-warning-light-5: #f3d19e;
  --el-color-warning-light-7: #f8e3c5;
  --el-color-warning-light-8: #faecd8;
  --el-color-warning-light-9: #fdf6ec;
  --el-color-warning-dark-2: #b88230;
  
  // 危险色
  --el-color-danger: #f56c6c;
  --el-color-danger-light-3: #f89898;
  --el-color-danger-light-5: #fab6b6;
  --el-color-danger-light-7: #fcd3d3;
  --el-color-danger-light-8: #fde2e2;
  --el-color-danger-light-9: #fef0f0;
  --el-color-danger-dark-2: #c45656;
  
  // 信息色
  --el-color-info: #909399;
  --el-color-info-light-3: #b1b3b8;
  --el-color-info-light-5: #c8c9cc;
  --el-color-info-light-7: #dedfe0;
  --el-color-info-light-8: #e9e9eb;
  --el-color-info-light-9: #f4f4f5;
  --el-color-info-dark-2: #73767a;
  
  // 文本色
  --el-text-color-primary: #303133;
  --el-text-color-regular: #606266;
  --el-text-color-secondary: #909399;
  --el-text-color-placeholder: #a8abb2;
  --el-text-color-disabled: #c0c4cc;
  
  // 边框色
  --el-border-color: #dcdfe6;
  --el-border-color-light: #e4e7ed;
  --el-border-color-lighter: #ebeef5;
  --el-border-color-extra-light: #f2f6fc;
  --el-border-color-dark: #d4d7de;
  --el-border-color-darker: #cdd0d6;
  
  // 填充色
  --el-fill-color: #f0f2f5;
  --el-fill-color-light: #f5f7fa;
  --el-fill-color-lighter: #fafafa;
  --el-fill-color-extra-light: #fafcff;
  --el-fill-color-dark: #ebedf0;
  --el-fill-color-darker: #e6e8eb;
  --el-fill-color-blank: #ffffff;
  
  // 背景色
  --el-bg-color: #ffffff;
  --el-bg-color-page: #f2f3f5;
  --el-bg-color-overlay: #ffffff;
  
  // 字体
  --el-font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  --el-font-size-extra-large: 20px;
  --el-font-size-large: 18px;
  --el-font-size-medium: 16px;
  --el-font-size-base: 14px;
  --el-font-size-small: 13px;
  --el-font-size-extra-small: 12px;
  
  // 圆角
  --el-border-radius-base: 4px;
  --el-border-radius-small: 2px;
  --el-border-radius-round: 20px;
  --el-border-radius-circle: 100%;
  
  // 阴影
  --el-box-shadow: 0 12px 32px 4px rgba(0, 0, 0, 0.04), 0 8px 20px rgba(0, 0, 0, 0.08);
  --el-box-shadow-light: 0 0 12px rgba(0, 0, 0, 0.12);
  --el-box-shadow-lighter: 0 0 6px rgba(0, 0, 0, 0.12);
  --el-box-shadow-dark: 0 16px 48px 16px rgba(0, 0, 0, 0.08), 0 12px 32px rgba(0, 0, 0, 0.12), 0 8px 16px -8px rgba(0, 0, 0, 0.16);
}

// 暗色主题
.dark {
  --el-bg-color: #141414;
  --el-bg-color-page: #0a0a0a;
  --el-bg-color-overlay: #1d1e1f;
  
  --el-text-color-primary: #e5eaf3;
  --el-text-color-regular: #cfd3dc;
  --el-text-color-secondary: #a3a6ad;
  --el-text-color-placeholder: #8d9095;
  --el-text-color-disabled: #6c6e72;
  
  --el-border-color: #4c4d4f;
  --el-border-color-light: #414243;
  --el-border-color-lighter: #363637;
  --el-border-color-extra-light: #2b2b2c;
  --el-border-color-dark: #58585b;
  --el-border-color-darker: #636466;
  
  --el-fill-color: #303133;
  --el-fill-color-light: #262727;
  --el-fill-color-lighter: #1d1d1d;
  --el-fill-color-extra-light: #191919;
  --el-fill-color-dark: #39393a;
  --el-fill-color-darker: #424243;
  --el-fill-color-blank: #141414;
}
```

### 组件样式覆盖
```scss
// styles/element-overrides.scss

// 按钮组件覆盖
.el-button {
  border-radius: 6px;
  font-weight: 500;
  
  &.el-button--primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    
    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }
  }
  
  &.el-button--success {
    background: linear-gradient(135deg, #81c784 0%, #4caf50 100%);
    border: none;
  }
}

// 表格组件覆盖
.el-table {
  border-radius: 8px;
  overflow: hidden;
  
  .el-table__header {
    th {
      background: #f8f9fa;
      color: #495057;
      font-weight: 600;
    }
  }
  
  .el-table__row {
    &:hover {
      background: #f8f9ff;
    }
  }
}

// 卡片组件覆盖
.el-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .el-card__header {
    border-bottom: 1px solid #f0f0f0;
    padding: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }
  }
  
  .el-card__body {
    padding: 20px;
  }
}

// 对话框组件覆盖
.el-dialog {
  border-radius: 12px;
  
  .el-dialog__header {
    padding: 20px 20px 10px;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }
  }
  
  .el-dialog__body {
    padding: 10px 20px 20px;
  }
  
  .el-dialog__footer {
    padding: 10px 20px 20px;
    text-align: right;
  }
}

// 表单组件覆盖
.el-form {
  .el-form-item {
    margin-bottom: 22px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }
    
    .el-form-item__error {
      font-size: 12px;
      color: #f56c6c;
    }
  }
}

// 输入框组件覆盖
.el-input {
  .el-input__inner {
    border-radius: 6px;
    
    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }
}

// 选择器组件覆盖
.el-select {
  .el-input__inner {
    border-radius: 6px;
  }
}

// 分页组件覆盖
.el-pagination {
  .el-pager li {
    border-radius: 4px;
    margin: 0 2px;
    
    &.active {
      background: #409eff;
      color: #fff;
    }
  }
  
  .btn-prev,
  .btn-next {
    border-radius: 4px;
  }
}
```

---

## 🔧 自定义指令规范

### 权限控制指令
```typescript
// directives/permission.ts
import type { Directive, DirectiveBinding } from 'vue'
import { useUserStore } from '@/stores/user'

/**
 * 权限指令
 * 使用方式：v-hasPermi="['user:add', 'user:edit']"
 */
export const hasPermi: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding
    const userStore = useUserStore()
    const permissions = userStore.permissions
    
    if (value && value instanceof Array && value.length > 0) {
      const hasPermission = permissions.some((permission: string) => {
        return value.includes(permission)
      })
      
      if (!hasPermission) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error('权限指令需要传入权限数组')
    }
  }
}

/**
 * 角色指令
 * 使用方式：v-hasRole="['admin', 'editor']"
 */
export const hasRole: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding
    const userStore = useUserStore()
    const roles = userStore.roles
    
    if (value && value instanceof Array && value.length > 0) {
      const hasRole = roles.some((role: string) => {
        return value.includes(role)
      })
      
      if (!hasRole) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error('角色指令需要传入角色数组')
    }
  }
}
```

### 防抖指令
```typescript
// directives/debounce.ts
import type { Directive, DirectiveBinding } from 'vue'

/**
 * 防抖指令
 * 使用方式：v-debounce:click="handleClick" 或 v-debounce:click.500="handleClick"
 */
export const debounce: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value, arg, modifiers } = binding
    const delay = Object.keys(modifiers)[0] ? Number(Object.keys(modifiers)[0]) : 300
    
    if (typeof value !== 'function') {
      throw new Error('防抖指令的值必须是一个函数')
    }
    
    let timer: NodeJS.Timeout | null = null
    
    const debounceHandler = (...args: any[]) => {
      if (timer) {
        clearTimeout(timer)
      }
      timer = setTimeout(() => {
        value.apply(this, args)
      }, delay)
    }
    
    el.addEventListener(arg || 'click', debounceHandler)
    
    // 保存处理函数，用于卸载时移除
    ;(el as any)._debounceHandler = debounceHandler
  },
  
  unmounted(el: HTMLElement, binding: DirectiveBinding) {
    const { arg } = binding
    const handler = (el as any)._debounceHandler
    
    if (handler) {
      el.removeEventListener(arg || 'click', handler)
      delete (el as any)._debounceHandler
    }
  }
}
```

---

*最后更新：2024年12月 | 维护团队：Fit-Manage项目组前端团队*