<template>
  <div class="diamond-position-manage">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">金刚位配置管理</h1>
      <p class="page-description">管理应用首页金刚位的显示内容和排序</p>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索金刚位名称"
          prefix-icon="Search"
          clearable
          style="width: 300px"
          @input="handleSearch"
        />
        <el-select
          v-model="statusFilter"
          placeholder="状态筛选"
          style="width: 120px; margin-left: 12px"
          @change="handleStatusFilter"
        >
          <el-option label="全部" value="" />
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </div>
      <div class="toolbar-right">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增金刚位
        </el-button>
        <el-button
          :disabled="selectedIds.length === 0"
          @click="handleBatchEdit"
        >
          批量编辑
        </el-button>
        <el-button @click="handlePreview">
          <el-icon><View /></el-icon>
          预览效果
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 金刚位网格配置区 -->
      <div class="grid-section">
        <div class="section-header">
          <h3>金刚位网格配置</h3>
          <el-button size="small" @click="handleSaveOrder">
            <el-icon><Check /></el-icon>
            保存排序
          </el-button>
        </div>
        <div class="diamond-grid" ref="gridContainer">
          <div
            v-for="item in sortedItems"
            :key="item.id"
            :data-id="item.id"
            class="diamond-item"
            :class="{ disabled: !item.isEnabled, dragging: draggedId === item.id }"
            draggable="true"
            @dragstart="handleDragStart"
            @dragover="handleDragOver"
            @dragenter="handleDragEnter"
            @dragleave="handleDragLeave"
            @drop="handleDrop"
            @dragend="handleDragEnd"
            @click="handleEdit(item)"
          >
            <div class="diamond-icon">
              <img :src="item.iconUrl" :alt="item.name" />
            </div>
            <div class="diamond-name">{{ item.name }}</div>
            <div v-if="!item.isEnabled" class="disabled-mask">
              <span>已禁用</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 多设备预览区 -->
      <div class="preview-section">
        <div class="section-header">
          <h3>多设备预览</h3>
          <div class="device-tabs">
            <el-radio-group v-model="previewDevice" @change="handleDeviceChange">
              <el-radio-button label="mobile">手机</el-radio-button>
              <el-radio-button label="tablet">平板</el-radio-button>
              <el-radio-button label="desktop">桌面</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="preview-container">
          <div :class="`preview-${previewDevice}`">
            <div class="preview-grid">
              <div
                v-for="item in enabledItems"
                :key="item.id"
                class="preview-item"
              >
                <div class="preview-icon">
                  <img :src="item.iconUrl" :alt="item.name" />
                </div>
                <div class="preview-name">{{ item.name }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 金刚位列表 -->
    <div class="table-section">
      <div class="section-header">
        <h3>金刚位列表</h3>
        <div class="table-actions">
          <el-button
            size="small"
            :disabled="selectedIds.length === 0"
            @click="handleBatchDelete"
          >
            批量删除
          </el-button>
        </div>
      </div>
      <el-table
        :data="filteredItems"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="sortOrder" label="排序" width="80" sortable />
        <el-table-column label="图标" width="80">
          <template #default="{ row }">
            <img :src="row.iconUrl" :alt="row.name" class="table-icon" />
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称" min-width="120" />
        <el-table-column prop="linkUrl" label="跳转链接" min-width="200" show-overflow-tooltip />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isEnabled ? 'success' : 'danger'">
              {{ row.isEnabled ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.updatedAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button
              size="small"
              :type="row.isEnabled ? 'warning' : 'success'"
              @click="handleToggleStatus(row)"
            >
              {{ row.isEnabled ? '禁用' : '启用' }}
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <DiamondPositionDialog
      v-model="dialogVisible"
      :form-data="currentItem"
      :is-edit="isEdit"
      @confirm="handleDialogConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, View, Check, Search } from '@element-plus/icons-vue'
import DiamondPositionDialog from './DiamondPositionDialog.vue'
import { useDiamondPosition } from '@/composables/useDiamondPosition'
import type { DiamondPosition } from '@/types/diamond-position'

// 组合式函数
const {
  items,
  loading,
  fetchItems,
  createItem,
  updateItem,
  deleteItem,
  updateSortOrder,
  toggleStatus
} = useDiamondPosition()

// 响应式数据
const searchKeyword = ref('')
const statusFilter = ref('')
const selectedIds = ref<number[]>([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const currentItem = ref<Partial<DiamondPosition>>({})
const previewDevice = ref('mobile')
const draggedId = ref<number | null>(null)
const gridContainer = ref<HTMLElement>()

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 计算属性
const sortedItems = computed(() => {
  return [...items.value].sort((a, b) => a.sortOrder - b.sortOrder)
})

const enabledItems = computed(() => {
  return sortedItems.value.filter(item => item.isEnabled)
})

const filteredItems = computed(() => {
  let result = [...items.value]
  
  // 搜索过滤
  if (searchKeyword.value) {
    result = result.filter(item => 
      item.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }
  
  // 状态过滤
  if (statusFilter.value !== '') {
    result = result.filter(item => 
      item.isEnabled === (statusFilter.value === '1')
    )
  }
  
  // 更新分页总数
  pagination.total = result.length
  
  // 分页处理
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return result.slice(start, end)
})

// 事件处理函数
const handleSearch = () => {
  pagination.currentPage = 1
}

const handleStatusFilter = () => {
  pagination.currentPage = 1
}

const handleAdd = () => {
  currentItem.value = {
    name: '',
    iconUrl: '',
    linkUrl: '',
    isEnabled: true,
    sortOrder: items.value.length + 1
  }
  isEdit.value = false
  dialogVisible.value = true
}

const handleEdit = (item: DiamondPosition) => {
  currentItem.value = { ...item }
  isEdit.value = true
  dialogVisible.value = true
}

const handleDelete = async (item: DiamondPosition) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除金刚位"${item.name}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteItem(item.id!)
    ElMessage.success('删除成功')
    await fetchItems()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleToggleStatus = async (item: DiamondPosition) => {
  try {
    await toggleStatus(item.id!, !item.isEnabled)
    ElMessage.success(`${item.isEnabled ? '禁用' : '启用'}成功`)
    await fetchItems()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const handleBatchEdit = () => {
  // 批量编辑逻辑
  ElMessage.info('批量编辑功能开发中')
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedIds.value.length} 个金刚位吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 批量删除逻辑
    for (const id of selectedIds.value) {
      await deleteItem(id)
    }
    
    ElMessage.success('批量删除成功')
    selectedIds.value = []
    await fetchItems()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const handlePreview = () => {
  // 预览功能
  ElMessage.info('预览功能开发中')
}

const handleSelectionChange = (selection: DiamondPosition[]) => {
  selectedIds.value = selection.map(item => item.id!)
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
}

const handleDeviceChange = () => {
  // 设备切换逻辑
}

const handleDialogConfirm = async (formData: Partial<DiamondPosition>) => {
  try {
    if (isEdit.value) {
      await updateItem(currentItem.value.id!, formData)
      ElMessage.success('更新成功')
    } else {
      await createItem(formData)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    await fetchItems()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  }
}

// 拖拽相关函数
const handleDragStart = (event: DragEvent) => {
  const target = event.target as HTMLElement
  draggedId.value = parseInt(target.dataset.id!)
  target.classList.add('dragging')
  
  event.dataTransfer!.effectAllowed = 'move'
  event.dataTransfer!.setData('text/html', target.outerHTML)
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  event.dataTransfer!.dropEffect = 'move'
}

const handleDragEnter = (event: DragEvent) => {
  event.preventDefault()
  const target = event.target as HTMLElement
  const diamondItem = target.closest('.diamond-item') as HTMLElement
  if (diamondItem && parseInt(diamondItem.dataset.id!) !== draggedId.value) {
    diamondItem.classList.add('drag-over')
  }
}

const handleDragLeave = (event: DragEvent) => {
  const target = event.target as HTMLElement
  const diamondItem = target.closest('.diamond-item') as HTMLElement
  if (diamondItem) {
    diamondItem.classList.remove('drag-over')
  }
}

const handleDrop = async (event: DragEvent) => {
  event.preventDefault()
  
  const target = event.target as HTMLElement
  const targetItem = target.closest('.diamond-item') as HTMLElement
  
  if (targetItem && draggedId.value) {
    const targetId = parseInt(targetItem.dataset.id!)
    
    if (targetId !== draggedId.value) {
      // 交换排序
      await swapItemOrder(draggedId.value, targetId)
      ElMessage.success('排序已更新，记得保存排序')
    }
  }
  
  // 清理样式
  document.querySelectorAll('.diamond-item').forEach(item => {
    item.classList.remove('drag-over')
  })
}

const handleDragEnd = (event: DragEvent) => {
  const target = event.target as HTMLElement
  target.classList.remove('dragging')
  draggedId.value = null
}

const swapItemOrder = async (id1: number, id2: number) => {
  const item1 = items.value.find(item => item.id === id1)
  const item2 = items.value.find(item => item.id === id2)
  
  if (item1 && item2) {
    const tempOrder = item1.sortOrder
    item1.sortOrder = item2.sortOrder
    item2.sortOrder = tempOrder
  }
}

const handleSaveOrder = async () => {
  try {
    const orderData = items.value.map(item => ({
      id: item.id!,
      sortOrder: item.sortOrder
    }))
    
    await updateSortOrder(orderData)
    ElMessage.success('排序保存成功')
  } catch (error) {
    ElMessage.error('排序保存失败')
  }
}

// 工具函数
const formatDate = (date: string) => {
  return new Date(date).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  fetchItems()
})
</script>

<style scoped lang="scss">
.diamond-position-manage {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    margin-bottom: 24px;
    
    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 8px 0;
    }
    
    .page-description {
      color: #6b7280;
      margin: 0;
    }
  }

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .toolbar-left {
      display: flex;
      align-items: center;
    }

    .toolbar-right {
      display: flex;
      gap: 12px;
    }
  }

  .main-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 24px;
    margin-bottom: 24px;

    @media (max-width: 1200px) {
      grid-template-columns: 1fr;
    }
  }

  .grid-section,
  .preview-section,
  .table-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
      }
    }
  }

  .diamond-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 16px;
    min-height: 300px;

    .diamond-item {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px 8px;
      border: 2px solid #e5e7eb;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
      user-select: none;

      &:hover {
        border-color: #3b82f6;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
      }

      &.dragging {
        opacity: 0.5;
        transform: rotate(5deg);
      }

      &.drag-over {
        border-color: #10b981;
        background-color: #ecfdf5;
      }

      &.disabled {
        opacity: 0.6;
        
        .disabled-mask {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 6px;
          font-size: 12px;
        }
      }

      .diamond-icon {
        width: 48px;
        height: 48px;
        margin-bottom: 8px;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 4px;
        }
      }

      .diamond-name {
        font-size: 12px;
        text-align: center;
        color: #374151;
        line-height: 1.2;
      }
    }
  }

  .preview-container {
    .preview-mobile {
      width: 200px;
      margin: 0 auto;
      
      .preview-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
      }
    }

    .preview-tablet {
      width: 300px;
      margin: 0 auto;
      
      .preview-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 12px;
      }
    }

    .preview-desktop {
      width: 360px;
      margin: 0 auto;
      
      .preview-grid {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 12px;
      }
    }

    .preview-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 8px;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      background: #f9fafb;

      .preview-icon {
        width: 32px;
        height: 32px;
        margin-bottom: 4px;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 2px;
        }
      }

      .preview-name {
        font-size: 10px;
        text-align: center;
        color: #6b7280;
        line-height: 1.2;
      }
    }
  }

  .table-icon {
    width: 32px;
    height: 32px;
    object-fit: cover;
    border-radius: 4px;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
}
</style>