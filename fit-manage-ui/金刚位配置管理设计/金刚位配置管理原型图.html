<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>金刚位配置管理端 - 原型图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: #fff;
            padding: 16px 24px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #1890ff;
            font-size: 24px;
            margin-bottom: 8px;
        }

        .header p {
            color: #666;
            font-size: 14px;
        }

        .toolbar {
            background: #fff;
            padding: 16px 24px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;
        }

        .search-box {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .search-box input {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            width: 200px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background: #40a9ff;
        }

        .btn-secondary {
            background: #f0f0f0;
            color: #333;
        }

        .btn-secondary:hover {
            background: #e6e6e6;
        }

        .btn-danger {
            background: #ff4d4f;
            color: white;
        }

        .btn-danger:hover {
            background: #ff7875;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 20px;
        }

        .config-section {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .section-header {
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
        }

        .grid-config {
            padding: 24px;
        }

        .device-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
        }

        .device-tab {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: #fff;
            cursor: pointer;
            transition: all 0.3s;
        }

        .device-tab.active {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .diamond-grid {
            display: grid;
            gap: 12px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
            border: 2px dashed #d9d9d9;
            min-height: 300px;
        }

        .diamond-grid.mobile {
            grid-template-columns: repeat(2, 1fr);
        }

        .diamond-grid.tablet {
            grid-template-columns: repeat(4, 1fr);
        }

        .diamond-grid.desktop {
            grid-template-columns: repeat(6, 1fr);
        }

        .diamond-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            cursor: move;
            transition: all 0.3s;
            position: relative;
            user-select: none;
        }

        .diamond-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .diamond-item.editing {
            border: 2px solid #1890ff;
        }

        .diamond-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
            z-index: 1000;
        }

        .diamond-item.drag-over {
            border: 2px dashed #1890ff;
            background: #f0f8ff;
        }

        .drag-placeholder {
            border: 2px dashed #d9d9d9;
            background: #f9f9f9;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 12px;
        }

        .diamond-icon {
            width: 40px;
            height: 40px;
            background: #1890ff;
            border-radius: 8px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .diamond-name {
            font-size: 12px;
            text-align: center;
            line-height: 1.2;
        }

        .item-actions {
            position: absolute;
            top: -8px;
            right: -8px;
            display: none;
        }

        .diamond-item:hover .item-actions {
            display: block;
        }

        .action-btn {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 12px;
            margin-left: 4px;
        }

        .edit-btn {
            background: #52c41a;
            color: white;
        }

        .delete-btn {
            background: #ff4d4f;
            color: white;
        }

        .config-list {
            padding: 0;
        }

        .list-table {
            width: 100%;
            border-collapse: collapse;
        }

        .list-table th,
        .list-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .list-table th {
            background: #fafafa;
            font-weight: 600;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .status-enabled {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .status-disabled {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffb3b3;
        }

        .pagination {
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #f0f0f0;
        }

        .preview-section {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .preview-content {
            padding: 24px;
        }

        .device-preview {
            border: 2px solid #d9d9d9;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 20px;
            background: #fff;
        }

        .device-preview.mobile {
            width: 320px;
            margin: 0 auto 20px;
        }

        .device-preview.tablet {
            width: 100%;
            max-width: 600px;
            margin: 0 auto 20px;
        }

        .device-preview.desktop {
            width: 100%;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: #fff;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            resize: vertical;
            min-height: 80px;
        }

        .form-input:focus,
        .form-textarea:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .icon-input-group {
            margin-bottom: 8px;
        }

        .form-help {
            color: #666;
            font-size: 12px;
            margin-top: 4px;
            display: block;
        }

        .icon-preview {
            margin-top: 12px;
            padding: 12px;
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            background-color: #fafafa;
        }

        .preview-container {
            margin-top: 8px;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            background-color: #fff;
        }

        .preview-placeholder {
            color: #999;
            font-size: 12px;
        }

        .preview-icon {
            font-size: 24px;
        }

        .preview-icon img {
            width: 24px;
            height: 24px;
            object-fit: contain;
        }

        .icon-type-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .icon-type-badge.url {
            background-color: #e6f7ff;
            color: #1890ff;
        }

        .icon-type-badge.emoji {
            background-color: #fff7e6;
            color: #fa8c16;
        }

        .icon-type-badge.svg {
            background-color: #f6ffed;
            color: #52c41a;
        }

        .icon-type-badge.base64 {
            background-color: #fff1f0;
            color: #ff4d4f;
        }

        .input-with-upload {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .textarea-with-upload {
            position: relative;
        }

        .textarea-with-upload .upload-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            z-index: 10;
        }

        .upload-btn {
            padding: 6px 12px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            white-space: nowrap;
            transition: background-color 0.3s;
        }

        .upload-btn:hover {
            background: #40a9ff;
        }

        .upload-progress {
            margin-top: 8px;
            padding: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            font-size: 12px;
            color: #666;
        }

        .upload-success {
            color: #52c41a;
        }

        .upload-error {
            color: #ff4d4f;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #999;
        }

        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                justify-content: center;
            }

            .search-box input {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>金刚位配置管理</h1>
            <p>管理首页金刚位的显示内容、排序和状态，支持多设备响应式预览</p>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="search-box">
                <input type="text" placeholder="搜索金刚位名称..." id="searchInput">
                <button class="btn btn-secondary" onclick="searchItems()">搜索</button>
            </div>
            <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                <button class="btn btn-primary" onclick="showAddModal()">+ 新增金刚位</button>
                <button class="btn btn-secondary" onclick="batchEdit()">批量编辑</button>
                <button class="btn btn-secondary" onclick="previewMode()">预览模式</button>
                <button class="btn btn-danger" onclick="batchDelete()">批量删除</button>
            </div>
        </div>

        <div class="main-content">
            <!-- 左侧：金刚位配置区域 -->
            <div class="config-section">
                <div class="section-header">
                    <div class="section-title">金刚位网格配置</div>
                    <div style="display: flex; gap: 8px;">
                        <button class="btn btn-secondary" onclick="saveOrder()">保存排序</button>
                        <button class="btn btn-secondary" onclick="resetOrder()">重置排序</button>
                    </div>
                </div>
                <div class="grid-config">
                    <div class="device-tabs">
                        <div class="device-tab active" onclick="switchDevice('mobile')">手机端 (2列)</div>
                        <div class="device-tab" onclick="switchDevice('tablet')">平板端 (4列)</div>
                        <div class="device-tab" onclick="switchDevice('desktop')">桌面端 (6列)</div>
                    </div>
                    <div class="diamond-grid mobile" id="diamondGrid">
                        <!-- 金刚位项目将通过JavaScript动态生成 -->
                    </div>
                    <div style="margin-top: 16px; padding: 12px; background: #f0f8ff; border-radius: 6px; font-size: 14px; color: #1890ff;">
                        💡 提示：拖拽金刚位可以调整排序，点击金刚位可以编辑内容
                    </div>
                </div>

                <!-- 配置列表 -->
                <div class="section-header">
                    <div class="section-title">金刚位列表</div>
                    <div style="font-size: 14px; color: #666;">共 <span id="totalCount">12</span> 个金刚位</div>
                </div>
                <div class="config-list">
                    <table class="list-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>
                                <th>排序</th>
                                <th>名称</th>
                                <th>图标</th>
                                <th>图标类型</th>
                                <th>链接</th>
                                <th>状态</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="configTableBody">
                            <!-- 表格内容将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>
                <div class="pagination">
                    <div>显示 1-10 条，共 12 条记录</div>
                    <div style="display: flex; gap: 8px;">
                        <button class="btn btn-secondary">上一页</button>
                        <button class="btn btn-primary">1</button>
                        <button class="btn btn-secondary">2</button>
                        <button class="btn btn-secondary">下一页</button>
                    </div>
                </div>
            </div>

            <!-- 右侧：多设备预览区域 -->
            <div class="preview-section">
                <div class="section-header">
                    <div class="section-title">多设备预览</div>
                    <button class="btn btn-secondary" onclick="refreshPreview()">刷新预览</button>
                </div>
                <div class="preview-content">
                    <h4 style="margin-bottom: 12px;">手机端效果</h4>
                    <div class="device-preview mobile">
                        <div class="diamond-grid mobile" id="mobilePreview">
                            <!-- 手机端预览内容 -->
                        </div>
                    </div>

                    <h4 style="margin-bottom: 12px;">平板端效果</h4>
                    <div class="device-preview tablet">
                        <div class="diamond-grid tablet" id="tabletPreview">
                            <!-- 平板端预览内容 -->
                        </div>
                    </div>

                    <h4 style="margin-bottom: 12px;">桌面端效果</h4>
                    <div class="device-preview desktop">
                        <div class="diamond-grid desktop" id="desktopPreview">
                            <!-- 桌面端预览内容 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑金刚位模态框 -->
    <div class="modal" id="editModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">新增金刚位</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <div class="form-group">
                        <label class="form-label">金刚位名称 *</label>
                        <input type="text" class="form-input" id="itemName" placeholder="请输入金刚位名称" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">图标类型 *</label>
                        <select class="form-input" id="iconType" onchange="handleIconTypeChange()">
                            <option value="url">URL链接</option>
                            <option value="emoji">表情符号</option>
                            <option value="svg">SVG代码</option>
                            <option value="base64">Base64编码</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">图标内容 *</label>
                        <div id="iconInputContainer">
                            <!-- URL类型输入框 -->
                            <div id="urlInput" class="icon-input-group">
                                <div class="input-with-upload">
                                    <input type="url" class="form-input" id="itemIconUrl" placeholder="请输入图标URL地址，如：https://example.com/icon.png">
                                    <button type="button" class="upload-btn" onclick="triggerFileUpload('url')">上传图片</button>
                                </div>
                                <input type="file" id="urlFileInput" accept="image/*" style="display: none;" onchange="handleFileUpload(event, 'url')">
                                <small class="form-help">支持网络链接、本地路径或上传图片文件</small>
                            </div>
                            <!-- 表情符号输入框 -->
                            <div id="emojiInput" class="icon-input-group" style="display: none;">
                                <input type="text" class="form-input" id="itemIconEmoji" placeholder="请输入表情符号，如：💰 🔧 💳" maxlength="10">
                                <small class="form-help">支持Unicode表情符号，长度不超过10个字符</small>
                            </div>
                            <!-- SVG代码输入框 -->
                            <div id="svgInput" class="icon-input-group" style="display: none;">
                                <div class="textarea-with-upload">
                                    <textarea class="form-input" id="itemIconSvg" rows="4" placeholder="请输入SVG代码，如：<svg viewBox=&quot;0 0 24 24&quot;>...</svg>"></textarea>
                                    <button type="button" class="upload-btn" onclick="triggerFileUpload('svg')">上传SVG</button>
                                </div>
                                <input type="file" id="svgFileInput" accept=".svg,image/svg+xml" style="display: none;" onchange="handleFileUpload(event, 'svg')">
                                <small class="form-help">请输入完整的SVG代码或上传SVG文件</small>
                            </div>
                            <!-- Base64输入框 -->
                            <div id="base64Input" class="icon-input-group" style="display: none;">
                                <div class="textarea-with-upload">
                                    <textarea class="form-input" id="itemIconBase64" rows="3" placeholder="请输入Base64编码，如：data:image/png;base64,iVBORw0..."></textarea>
                                    <button type="button" class="upload-btn" onclick="triggerFileUpload('base64')">上传图片</button>
                                </div>
                                <input type="file" id="base64FileInput" accept="image/*" style="display: none;" onchange="handleFileUpload(event, 'base64')">
                                <small class="form-help">请输入完整的Base64编码或上传图片文件</small>
                            </div>
                        </div>
                        <!-- 图标预览 -->
                        <div class="icon-preview">
                            <label class="form-label">图标预览：</label>
                            <div id="iconPreview" class="preview-container">
                                <span class="preview-placeholder">请选择图标类型并输入内容</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">跳转链接</label>
                        <input type="url" class="form-input" id="itemUrl" placeholder="请输入跳转链接">
                    </div>
                    <div class="form-group">
                        <label class="form-label">排序序号</label>
                        <input type="number" class="form-input" id="itemSort" placeholder="数字越小排序越靠前" min="0">
                    </div>
                    <div class="form-group">
                        <label class="form-label">状态</label>
                        <select class="form-input" id="itemStatus">
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">描述</label>
                        <textarea class="form-textarea" id="itemDescription" placeholder="请输入金刚位描述"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button class="btn btn-primary" onclick="saveItem()">保存</button>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据（包含不同图标类型）
        let diamondItems = [
            { id: 1, name: '我的订单', icon: '📦', iconType: 'emoji', url: '/orders', sortOrder: 1, status: 1, description: '查看我的订单信息' },
            { id: 2, name: '我的钱包', icon: '💰', iconType: 'emoji', url: '/wallet', sortOrder: 2, status: 1, description: '管理我的钱包' },
            { id: 3, name: '优惠券', icon: 'https://example.com/icons/coupon.png', iconType: 'url', url: '/coupons', sortOrder: 3, status: 1, description: '我的优惠券' },
            { id: 4, name: '积分商城', icon: '🏪', iconType: 'emoji', url: '/points', sortOrder: 4, status: 1, description: '积分兑换商城' },
            { id: 5, name: '客服中心', icon: '<svg viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>', iconType: 'svg', url: '/service', sortOrder: 5, status: 1, description: '联系客服' },
            { id: 6, name: '设置', icon: '⚙️', iconType: 'emoji', url: '/settings', sortOrder: 6, status: 1, description: '应用设置' },
            { id: 7, name: '帮助中心', icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', iconType: 'base64', url: '/help', sortOrder: 7, status: 0, description: '获取帮助' },
            { id: 8, name: '关于我们', icon: 'ℹ️', iconType: 'emoji', url: '/about', sortOrder: 8, status: 0, description: '了解我们' },
            { id: 9, name: '反馈建议', icon: '💬', iconType: 'emoji', url: '/feedback', sortOrder: 9, status: 1, description: '提交反馈' },
            { id: 10, name: '邀请好友', icon: 'https://example.com/icons/invite.png', iconType: 'url', url: '/invite', sortOrder: 10, status: 1, description: '邀请好友获得奖励' },
            { id: 11, name: '签到', icon: '📅', iconType: 'emoji', url: '/checkin', sortOrder: 11, status: 1, description: '每日签到' },
            { id: 12, name: '活动中心', icon: '🎉', iconType: 'emoji', url: '/activities', sortOrder: 12, status: 1, description: '参与活动' }
        ];

        let currentDevice = 'mobile';
        let editingId = null;

        // 图标类型处理函数
        function handleIconTypeChange() {
            const iconType = document.getElementById('iconType').value;
            const inputGroups = document.querySelectorAll('.icon-input-group');

            // 隐藏所有输入组
            inputGroups.forEach(group => group.style.display = 'none');

            // 显示对应的输入组
            const targetGroup = document.getElementById(iconType + 'Input');
            if (targetGroup) {
                targetGroup.style.display = 'block';
            }

            // 更新预览
            updateIconPreview();
        }

        // 更新图标预览
        function updateIconPreview() {
            const iconType = document.getElementById('iconType').value;
            const previewContainer = document.getElementById('iconPreview');
            let iconContent = '';

            switch (iconType) {
                case 'url':
                    iconContent = document.getElementById('itemIconUrl').value;
                    if (iconContent) {
                        previewContainer.innerHTML = `<div class="preview-icon"><img src="${iconContent}" alt="图标预览" onerror="this.style.display='none'"></div>`;
                    } else {
                        previewContainer.innerHTML = '<span class="preview-placeholder">请输入图标URL</span>';
                    }
                    break;
                case 'emoji':
                    iconContent = document.getElementById('itemIconEmoji').value;
                    if (iconContent) {
                        previewContainer.innerHTML = `<div class="preview-icon">${iconContent}</div>`;
                    } else {
                        previewContainer.innerHTML = '<span class="preview-placeholder">请输入表情符号</span>';
                    }
                    break;
                case 'svg':
                    iconContent = document.getElementById('itemIconSvg').value;
                    if (iconContent && iconContent.trim().startsWith('<svg')) {
                        previewContainer.innerHTML = `<div class="preview-icon">${iconContent}</div>`;
                    } else {
                        previewContainer.innerHTML = '<span class="preview-placeholder">请输入SVG代码</span>';
                    }
                    break;
                case 'base64':
                    iconContent = document.getElementById('itemIconBase64').value;
                    if (iconContent && iconContent.startsWith('data:image/')) {
                        previewContainer.innerHTML = `<div class="preview-icon"><img src="${iconContent}" alt="图标预览" onerror="this.style.display='none'"></div>`;
                    } else {
                        previewContainer.innerHTML = '<span class="preview-placeholder">请输入Base64编码</span>';
                    }
                    break;
                default:
                    previewContainer.innerHTML = '<span class="preview-placeholder">请选择图标类型</span>';
            }
        }

        // 获取当前图标内容
        function getCurrentIconContent() {
            const iconType = document.getElementById('iconType').value;
            switch (iconType) {
                case 'url':
                    return document.getElementById('itemIconUrl').value;
                case 'emoji':
                    return document.getElementById('itemIconEmoji').value;
                case 'svg':
                    return document.getElementById('itemIconSvg').value;
                case 'base64':
                    return document.getElementById('itemIconBase64').value;
                default:
                    return '';
            }
        }

        // 设置图标内容
        function setIconContent(iconType, iconContent) {
            // 设置图标类型
            document.getElementById('iconType').value = iconType;
            handleIconTypeChange();

            // 设置对应的图标内容
            switch (iconType) {
                case 'url':
                    document.getElementById('itemIconUrl').value = iconContent;
                    break;
                case 'emoji':
                    document.getElementById('itemIconEmoji').value = iconContent;
                    break;
                case 'svg':
                    document.getElementById('itemIconSvg').value = iconContent;
                    break;
                case 'base64':
                    document.getElementById('itemIconBase64').value = iconContent;
                    break;
            }

            updateIconPreview();
        }

        // 渲染图标（根据类型）
        function renderIcon(icon, iconType) {
            switch (iconType) {
                case 'url':
                    return `<img src="${icon}" alt="图标" style="width: 20px; height: 20px; object-fit: contain;">`;
                case 'emoji':
                    return icon;
                case 'svg':
                    return icon;
                case 'base64':
                    return `<img src="${icon}" alt="图标" style="width: 20px; height: 20px; object-fit: contain;">`;
                default:
                    return icon;
            }
        }

        // 获取图标类型标签
        function getIconTypeLabel(iconType) {
            const labels = {
                'url': 'URL',
                'emoji': '表情',
                'svg': 'SVG',
                'base64': 'Base64'
            };
            const label = labels[iconType] || 'URL';
            return `<span class="icon-type-badge ${iconType}">${label}</span>`;
        }

        // 验证图标内容
        function validateIconContent(iconType, icon) {
            switch (iconType) {
                case 'url':
                    if (!icon.startsWith('http://') && !icon.startsWith('https://') && !icon.startsWith('/')) {
                        alert('URL类型的图标必须以 http://、https:// 或 / 开头');
                        return false;
                    }
                    break;
                case 'svg':
                    if (!icon.trim().startsWith('<svg') || !icon.trim().endsWith('</svg>')) {
                        alert('SVG类型的图标必须以 <svg 开头，以 </svg> 结尾');
                        return false;
                    }
                    break;
                case 'base64':
                    if (!icon.startsWith('data:image/')) {
                        alert('Base64类型的图标必须以 data:image/ 开头');
                        return false;
                    }
                    break;
                case 'emoji':
                    if (icon.length > 10) {
                        alert('表情符号类型的图标长度不能超过10个字符');
                        return false;
                    }
                    break;
                default:
                    alert('不支持的图标类型');
                    return false;
            }
            return true;
        }

        // 触发文件上传
        function triggerFileUpload(type) {
            const fileInput = document.getElementById(type + 'FileInput');
            fileInput.click();
        }

        // 处理文件上传
        function handleFileUpload(event, type) {
            const file = event.target.files[0];
            if (!file) return;

            // 显示上传进度
            showUploadProgress(type, '正在处理文件...');

            // 根据类型处理文件
            switch (type) {
                case 'url':
                    handleImageToUrl(file, type);
                    break;
                case 'svg':
                    handleSvgFile(file, type);
                    break;
                case 'base64':
                    handleImageToBase64(file, type);
                    break;
            }
        }

        // 处理图片文件转URL（模拟上传到服务器）
        function handleImageToUrl(file, type) {
            // 验证文件类型
            if (!file.type.startsWith('image/')) {
                showUploadError(type, '请选择图片文件');
                return;
            }

            // 模拟上传到服务器，实际应该调用上传API
            setTimeout(() => {
                const mockUrl = `https://example.com/uploads/${Date.now()}_${file.name}`;
                document.getElementById('itemIconUrl').value = mockUrl;
                updateIconPreview();
                showUploadSuccess(type, '图片上传成功');
            }, 1500);
        }

        // 处理SVG文件
        function handleSvgFile(file, type) {
            // 验证文件类型
            if (file.type !== 'image/svg+xml' && !file.name.endsWith('.svg')) {
                showUploadError(type, '请选择SVG文件');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const svgContent = e.target.result;

                // 验证SVG内容
                if (!svgContent.trim().startsWith('<svg')) {
                    showUploadError(type, '无效的SVG文件');
                    return;
                }

                document.getElementById('itemIconSvg').value = svgContent;
                updateIconPreview();
                showUploadSuccess(type, 'SVG文件读取成功');
            };
            reader.onerror = function() {
                showUploadError(type, '文件读取失败');
            };
            reader.readAsText(file);
        }

        // 处理图片文件转Base64
        function handleImageToBase64(file, type) {
            // 验证文件类型
            if (!file.type.startsWith('image/')) {
                showUploadError(type, '请选择图片文件');
                return;
            }

            // 验证文件大小（限制为2MB）
            if (file.size > 2 * 1024 * 1024) {
                showUploadError(type, '图片文件大小不能超过2MB');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const base64Content = e.target.result;
                document.getElementById('itemIconBase64').value = base64Content;
                updateIconPreview();
                showUploadSuccess(type, '图片转换为Base64成功');
            };
            reader.onerror = function() {
                showUploadError(type, '文件读取失败');
            };
            reader.readAsDataURL(file);
        }

        // 显示上传进度
        function showUploadProgress(type, message) {
            const container = document.getElementById(type + 'Input');
            let progressDiv = container.querySelector('.upload-progress');

            if (!progressDiv) {
                progressDiv = document.createElement('div');
                progressDiv.className = 'upload-progress';
                container.appendChild(progressDiv);
            }

            progressDiv.textContent = message;
            progressDiv.className = 'upload-progress';
        }

        // 显示上传成功
        function showUploadSuccess(type, message) {
            const container = document.getElementById(type + 'Input');
            let progressDiv = container.querySelector('.upload-progress');

            if (!progressDiv) {
                progressDiv = document.createElement('div');
                progressDiv.className = 'upload-progress';
                container.appendChild(progressDiv);
            }

            progressDiv.textContent = message;
            progressDiv.className = 'upload-progress upload-success';

            // 3秒后隐藏消息
            setTimeout(() => {
                if (progressDiv) {
                    progressDiv.remove();
                }
            }, 3000);
        }

        // 显示上传错误
        function showUploadError(type, message) {
            const container = document.getElementById(type + 'Input');
            let progressDiv = container.querySelector('.upload-progress');

            if (!progressDiv) {
                progressDiv = document.createElement('div');
                progressDiv.className = 'upload-progress';
                container.appendChild(progressDiv);
            }

            progressDiv.textContent = message;
            progressDiv.className = 'upload-progress upload-error';

            // 5秒后隐藏消息
            setTimeout(() => {
                if (progressDiv) {
                    progressDiv.remove();
                }
            }, 5000);
        }

        // 初始化页面
        function init() {
            renderGrid();
            renderTable();
            renderPreviews();

            // 初始化图标类型选择器
            handleIconTypeChange();

            // 为图标输入框添加事件监听器
            document.getElementById('itemIconUrl').addEventListener('input', updateIconPreview);
            document.getElementById('itemIconEmoji').addEventListener('input', updateIconPreview);
            document.getElementById('itemIconSvg').addEventListener('input', updateIconPreview);
            document.getElementById('itemIconBase64').addEventListener('input', updateIconPreview);
        }

        // 渲染金刚位网格
        function renderGrid() {
            const grid = document.getElementById('diamondGrid');
            grid.className = `diamond-grid ${currentDevice}`;
            
            const enabledItems = diamondItems.filter(item => item.status === 1).sort((a, b) => a.sortOrder - b.sortOrder);
            
            grid.innerHTML = enabledItems.map(item => `
                <div class="diamond-item" 
                     data-id="${item.id}" 
                     draggable="true"
                     onclick="editItem(${item.id})"
                     ondragstart="handleDragStart(event)"
                     ondragover="handleDragOver(event)"
                     ondrop="handleDrop(event)"
                     ondragend="handleDragEnd(event)"
                     ondragenter="handleDragEnter(event)"
                     ondragleave="handleDragLeave(event)">
                    <div class="diamond-icon">${item.icon}</div>
                    <div class="diamond-name">${item.name}</div>
                    <div class="item-actions">
                        <button class="action-btn edit-btn" onclick="event.stopPropagation(); editItem(${item.id})">✏️</button>
                        <button class="action-btn delete-btn" onclick="event.stopPropagation(); deleteItem(${item.id})">🗑️</button>
                    </div>
                </div>
            `).join('');
        }

        // 渲染配置表格
        function renderTable() {
            const tbody = document.getElementById('configTableBody');
            tbody.innerHTML = diamondItems.map(item => `
                <tr>
                    <td><input type="checkbox" value="${item.id}"></td>
                    <td>${item.sortOrder}</td>
                    <td>${item.name}</td>
                    <td style="font-size: 20px;">${renderIcon(item.icon, item.iconType)}</td>
                    <td><span class="icon-type-badge">${getIconTypeLabel(item.iconType)}</span></td>
                    <td><a href="${item.url}" target="_blank">${item.url}</a></td>
                    <td><span class="status-badge ${item.status ? 'status-enabled' : 'status-disabled'}">${item.status ? '启用' : '禁用'}</span></td>
                    <td>2024-01-15 10:30</td>
                    <td>
                        <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;" onclick="editItem(${item.id})">编辑</button>
                        <button class="btn btn-danger" style="padding: 4px 8px; font-size: 12px; margin-left: 4px;" onclick="deleteItem(${item.id})">删除</button>
                    </td>
                </tr>
            `).join('');
            
            document.getElementById('totalCount').textContent = diamondItems.length;
        }

        // 渲染预览
        function renderPreviews() {
            const enabledItems = diamondItems.filter(item => item.status === 1).sort((a, b) => a.sortOrder - b.sortOrder);
            
            ['mobile', 'tablet', 'desktop'].forEach(device => {
                const preview = document.getElementById(`${device}Preview`);
                preview.className = `diamond-grid ${device}`;
                preview.innerHTML = enabledItems.map(item => `
                    <div class="diamond-item">
                        <div class="diamond-icon">${item.icon}</div>
                        <div class="diamond-name">${item.name}</div>
                    </div>
                `).join('');
            });
        }

        // 切换设备视图
        function switchDevice(device) {
            currentDevice = device;
            document.querySelectorAll('.device-tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
            renderGrid();
        }

        // 显示新增模态框
        function showAddModal() {
            editingId = null;
            document.getElementById('modalTitle').textContent = '新增金刚位';
            document.getElementById('editForm').reset();
            document.getElementById('editModal').classList.add('show');
        }

        // 编辑金刚位
        function editItem(id) {
            editingId = id;
            const item = diamondItems.find(item => item.id === id);
            if (item) {
                document.getElementById('modalTitle').textContent = '编辑金刚位';
                document.getElementById('itemName').value = item.name;

                // 设置图标类型和内容
                setIconContent(item.iconType || 'emoji', item.icon);

                document.getElementById('itemUrl').value = item.url;
                document.getElementById('itemSort').value = item.sortOrder;
                document.getElementById('itemStatus').value = item.status;
                document.getElementById('itemDescription').value = item.description;
                document.getElementById('editModal').classList.add('show');
            }
        }

        // 保存金刚位
        function saveItem() {
            const name = document.getElementById('itemName').value;
            const iconType = document.getElementById('iconType').value;
            const icon = getCurrentIconContent();
            const url = document.getElementById('itemUrl').value;
            const sortOrder = parseInt(document.getElementById('itemSort').value) || 0;
            const status = parseInt(document.getElementById('itemStatus').value);
            const description = document.getElementById('itemDescription').value;

            if (!name) {
                alert('请输入金刚位名称');
                return;
            }

            if (!icon) {
                alert('请输入图标内容');
                return;
            }

            // 验证图标类型和内容的匹配性
            if (!validateIconContent(iconType, icon)) {
                return;
            }

            if (editingId) {
                // 更新
                const item = diamondItems.find(item => item.id === editingId);
                if (item) {
                    item.name = name;
                    item.icon = icon;
                    item.iconType = iconType;
                    item.url = url;
                    item.sortOrder = sortOrder;
                    item.status = status;
                    item.description = description;
                }
            } else {
                // 新增
                const newId = Math.max(...diamondItems.map(item => item.id)) + 1;
                diamondItems.push({
                    id: newId,
                    name,
                    icon,
                    iconType,
                    url,
                    sortOrder,
                    status,
                    description
                });
            }

            closeModal();
            renderGrid();
            renderTable();
            renderPreviews();
        }

        // 删除金刚位
        function deleteItem(id) {
            if (confirm('确定要删除这个金刚位吗？')) {
                diamondItems = diamondItems.filter(item => item.id !== id);
                renderGrid();
                renderTable();
                renderPreviews();
            }
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('editModal').classList.remove('show');
        }

        // 搜索功能
        function searchItems() {
            const keyword = document.getElementById('searchInput').value.toLowerCase();
            // 这里可以实现搜索逻辑
            console.log('搜索关键词:', keyword);
        }

        // 批量编辑
        function batchEdit() {
            alert('批量编辑功能');
        }

        // 预览模式
        function previewMode() {
            alert('预览模式功能');
        }

        // 批量删除
        function batchDelete() {
            const checkboxes = document.querySelectorAll('#configTableBody input[type="checkbox"]:checked');
            if (checkboxes.length === 0) {
                alert('请选择要删除的项目');
                return;
            }
            if (confirm(`确定要删除选中的 ${checkboxes.length} 个金刚位吗？`)) {
                const idsToDelete = Array.from(checkboxes).map(cb => parseInt(cb.value));
                diamondItems = diamondItems.filter(item => !idsToDelete.includes(item.id));
                renderGrid();
                renderTable();
                renderPreviews();
            }
        }

        // 保存排序
        function saveOrder() {
            alert('排序已保存');
        }

        // 重置排序
        function resetOrder() {
            if (confirm('确定要重置排序吗？')) {
                diamondItems.forEach((item, index) => {
                    item.sortOrder = index + 1;
                });
                renderGrid();
                renderTable();
                renderPreviews();
            }
        }

        // 刷新预览
        function refreshPreview() {
            renderPreviews();
        }

        // 全选功能
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('#configTableBody input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = selectAll.checked);
        }

        // 拖拽功能实现
        let draggedElement = null;
        let draggedId = null;

        function handleDragStart(event) {
            draggedElement = event.target;
            draggedId = parseInt(event.target.dataset.id);
            event.target.classList.add('dragging');
            
            // 设置拖拽数据
            event.dataTransfer.effectAllowed = 'move';
            event.dataTransfer.setData('text/html', event.target.outerHTML);
            
            // 显示拖拽提示
            showDragHint();
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.dataTransfer.dropEffect = 'move';
        }

        function handleDragEnter(event) {
            event.preventDefault();
            if (event.target.classList.contains('diamond-item') && event.target !== draggedElement) {
                event.target.classList.add('drag-over');
            }
        }

        function handleDragLeave(event) {
            if (event.target.classList.contains('diamond-item')) {
                event.target.classList.remove('drag-over');
            }
        }

        function handleDrop(event) {
            event.preventDefault();
            
            const targetElement = event.target.closest('.diamond-item');
            if (targetElement && targetElement !== draggedElement) {
                const targetId = parseInt(targetElement.dataset.id);
                
                // 交换排序
                swapItemOrder(draggedId, targetId);
                
                // 重新渲染
                renderGrid();
                renderTable();
                renderPreviews();
                
                // 显示成功提示
                showSuccessMessage('排序已更新，记得点击"保存排序"按钮保存更改');
            }
            
            // 清理样式
            document.querySelectorAll('.diamond-item').forEach(item => {
                item.classList.remove('drag-over');
            });
            
            hideDragHint();
        }

        function handleDragEnd(event) {
            event.target.classList.remove('dragging');
            document.querySelectorAll('.diamond-item').forEach(item => {
                item.classList.remove('drag-over');
            });
            draggedElement = null;
            draggedId = null;
            hideDragHint();
        }

        function swapItemOrder(id1, id2) {
            const item1 = diamondItems.find(item => item.id === id1);
            const item2 = diamondItems.find(item => item.id === id2);
            
            if (item1 && item2) {
                const tempOrder = item1.sortOrder;
                item1.sortOrder = item2.sortOrder;
                item2.sortOrder = tempOrder;
            }
        }

        function showDragHint() {
            const hint = document.createElement('div');
            hint.id = 'dragHint';
            hint.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #1890ff;
                color: white;
                padding: 12px 16px;
                border-radius: 6px;
                font-size: 14px;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                animation: slideIn 0.3s ease;
            `;
            hint.innerHTML = '🔄 拖拽到目标位置来调整排序';
            document.body.appendChild(hint);
        }

        function hideDragHint() {
            const hint = document.getElementById('dragHint');
            if (hint) {
                hint.remove();
            }
        }

        function showSuccessMessage(message) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: #52c41a;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            `;
            toast.innerHTML = `✅ ${message}`;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
        
        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>