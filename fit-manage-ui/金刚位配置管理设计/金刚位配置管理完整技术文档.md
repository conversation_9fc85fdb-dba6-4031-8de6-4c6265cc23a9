# 金刚位配置管理系统完整技术文档

## 📋 文档概述

本文档详细描述了金刚位配置管理系统的完整技术实现方案，包括系统架构、数据库设计、API接口、前端组件、响应式布局、部署方案等全方位技术内容。

## 🎯 项目概述

### 项目背景
金刚位是移动应用首页的核心功能入口，用户通过点击金刚位快速访问各种服务。随着业务发展，需要一个灵活、易用的配置管理系统来动态管理金刚位的显示、排序和内容。

### 核心目标
- **灵活配置**: 支持金刚位的动态增删改查
- **拖拽排序**: 提供直观的拖拽排序功能
- **多端适配**: 支持手机、平板、桌面端响应式显示
- **实时预览**: 配置变更实时预览效果
- **高性能**: 优化加载速度和用户体验

## 🏗️ 系统架构

### 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端管理界面   │    │    后端API服务   │    │    数据库存储    │
│                │    │                │    │                │
│ Vue 3 + TS     │◄──►│ Spring Boot    │◄──►│ MySQL 8.0      │
│ Element Plus   │    │ RESTful API    │    │ 单表设计        │
│ CSS Grid       │    │ JSON响应       │    │ 极简架构        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 技术栈选择

#### 前端技术栈
- **Vue 3**: 现代化前端框架，支持Composition API
- **TypeScript**: 类型安全，提升开发效率
- **Element Plus**: 企业级UI组件库
- **CSS Grid**: 现代布局技术，支持响应式设计
- **Vite**: 快速构建工具

#### 后端技术栈
- **Spring Boot 3.x**: 企业级Java框架
- **Spring Data JPA**: 数据访问层
- **MySQL 8.0**: 关系型数据库
- **Maven**: 项目管理工具

#### 开发工具
- **IDE**: IntelliJ IDEA / VS Code
- **版本控制**: Git
- **API测试**: Postman
- **数据库管理**: MySQL Workbench

## 🗄️ 数据库设计

### 表结构设计

#### oto_diamond_position 表
```sql
CREATE TABLE oto_diamond_position (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    name VARCHAR(100) NOT NULL COMMENT '金刚位名称',
    icon TEXT COMMENT '图标内容（URL、SVG代码、Base64等）',
    icon_type VARCHAR(20) DEFAULT 'url' COMMENT '图标类型：url-链接地址，svg-SVG代码，base64-Base64编码，emoji-表情符号',
    url VARCHAR(500) COMMENT '跳转链接',
    sort_order INT DEFAULT 0 COMMENT '排序序号',
    status TINYINT(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    description VARCHAR(500) COMMENT '描述',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    tenant_id VARCHAR(20) DEFAULT '000000' COMMENT '租户编号',
    create_dept BIGINT COMMENT '创建部门',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',

    INDEX idx_sort_order (sort_order),
    INDEX idx_status (status),
    INDEX idx_icon_type (icon_type),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='金刚位配置表';
```

### 图标类型设计

#### 支持的图标类型

| 类型 | 值 | 描述 | 示例 | 前端处理方式 |
|------|-----|------|------|-------------|
| URL | url | 网络链接或本地路径 | `https://example.com/icon.png` | `<img src={icon} />` |
| SVG | svg | SVG矢量图代码 | `<svg viewBox="0 0 24 24">...</svg>` | `dangerouslySetInnerHTML` |
| Base64 | base64 | Base64编码图片 | `data:image/png;base64,iVBORw0...` | `<img src={icon} />` |
| 表情符号 | emoji | Unicode表情符号 | `💰` `🔧` `💳` | `<span>{icon}</span>` |

#### 图标类型验证规则

1. **URL类型**: 必须以 `http://`, `https://` 或 `/` 开头
2. **SVG类型**: 必须以 `<svg` 开头，以 `</svg>` 结尾
3. **Base64类型**: 必须以 `data:image/` 开头
4. **表情符号类型**: 长度不超过10个字符

#### 自动类型检测

如果未指定图标类型，系统会根据图标内容自动检测：
- 以 `http://`, `https://`, `/` 开头 → `url`
- 以 `<svg` 开头且以 `</svg>` 结尾 → `svg`
- 以 `data:image/` 开头 → `base64`
- 其他情况 → `emoji`

### 索引策略
- **主键索引**: id字段，保证唯一性和查询性能
- **排序索引**: sort_order字段，优化排序查询
- **状态索引**: status字段，快速筛选启用状态
- **图标类型索引**: icon_type字段，支持按图标类型筛选
- **时间索引**: created_time字段，支持时间范围查询

### 数据示例
```sql
-- 展示不同图标类型的使用示例
INSERT INTO oto_diamond_position (name, icon, icon_type, url, sort_order, status, description) VALUES
('扫一扫', 'https://example.com/icons/scan.png', 'url', '/scan', 1, 1, '扫码功能'),
('付款码', '💰', 'emoji', '/payment', 2, 1, '付款码功能'),
('充值', '<svg viewBox="0 0 24 24"><path d="M12 2L2 7v10c0 5.55 3.84 10 9 10s9-4.45 9-10V7l-10-5z"/></svg>', 'svg', '/recharge', 3, 1, '账户充值'),
('转账', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64', '/transfer', 4, 0, '转账功能'),
('理财', 'https://example.com/icons/finance.png', 'url', '/finance', 5, 1, '理财产品'),
('信用卡', '💳', 'emoji', '/credit-card', 6, 1, '信用卡服务'),
('保洁服务', 'https://example.com/icons/cleaning.png', 'url', '/pages/service/cleaning', 7, 1, '家庭保洁服务'),
('维修服务', '🔧', 'emoji', '/pages/service/repair', 8, 1, '家电维修服务');
```

## 🔌 后端API设计

### API接口规范

#### 基础响应格式
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 分页响应格式
```json
{
  "success": true,
  "data": {
    "data": [],
    "total": 100,
    "page": 1,
    "pageSize": 20,
    "totalPages": 5
  },
  "message": "查询成功"
}
```

### 核心API接口

#### 1. 获取金刚位列表
```http
GET /api/diamond-positions
```

**请求参数**:
- `keyword` (string, optional): 搜索关键词
- `status` (int, optional): 状态筛选 (0-禁用，1-启用)
- `iconType` (string, optional): 图标类型筛选 (url/svg/base64/emoji)
- `page` (int, optional): 页码，默认1
- `pageSize` (int, optional): 每页大小，默认20

**响应示例**:
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": 1,
        "name": "扫一扫",
        "icon": "https://example.com/icons/scan.png",
        "iconType": "url",
        "url": "/scan",
        "sortOrder": 1,
        "status": 1,
        "description": "扫码功能",
        "createdTime": "2024-01-01T00:00:00Z",
        "updatedTime": "2024-01-01T00:00:00Z",
        "createdBy": "admin",
        "updatedBy": "admin"
      },
      {
        "id": 2,
        "name": "付款码",
        "icon": "💰",
        "iconType": "emoji",
        "url": "/payment",
        "sortOrder": 2,
        "status": 1,
        "description": "付款码功能",
        "createdTime": "2024-01-01T00:00:00Z",
        "updatedTime": "2024-01-01T00:00:00Z",
        "createdBy": "admin",
        "updatedBy": "admin"
      }
    ],
    "total": 8,
    "page": 1,
    "pageSize": 20,
    "totalPages": 1
  },
  "message": "查询成功"
}
```

#### 2. 创建金刚位
```http
POST /api/diamond-positions
```

**请求体**:
```json
{
  "name": "新功能",
  "icon": "https://example.com/icons/new.png",
  "iconType": "url",
  "url": "/new-feature",
  "sortOrder": 7,
  "status": 1,
  "description": "新功能描述"
}
```

**图标类型示例**:
```json
// URL类型
{
  "name": "扫一扫",
  "icon": "https://example.com/icons/scan.png",
  "iconType": "url",
  "url": "/scan"
}

// 表情符号类型
{
  "name": "付款码",
  "icon": "💰",
  "iconType": "emoji",
  "url": "/payment"
}

// SVG类型
{
  "name": "充值",
  "icon": "<svg viewBox=\"0 0 24 24\"><path d=\"M12 2L2 7v10c0 5.55 3.84 10 9 10s9-4.45 9-10V7l-10-5z\"/></svg>",
  "iconType": "svg",
  "url": "/recharge"
}

// Base64类型
{
  "name": "转账",
  "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
  "iconType": "base64",
  "url": "/transfer"
}
```

#### 3. 更新金刚位
```http
PUT /api/diamond-positions/{id}
```

#### 4. 删除金刚位
```http
DELETE /api/diamond-positions/{id}
```

#### 5. 批量操作
```http
# 批量删除
DELETE /api/diamond-positions/batch
{
  "ids": [1, 2, 3]
}

# 批量更新状态
PATCH /api/diamond-positions/batch/status
{
  "ids": [1, 2, 3],
  "status": 0
}

# 更新排序
PUT /api/diamond-positions/sort-order
{
  "items": [
    {"id": 1, "sortOrder": 2},
    {"id": 2, "sortOrder": 1}
  ]
}
```

### 后端实现代码

#### Controller层
```java
@RestController
@RequestMapping("/api/diamond-positions")
@CrossOrigin(origins = "*")
public class DiamondPositionController {
    
    @Autowired
    private DiamondPositionService diamondPositionService;
    
    @GetMapping
    public ApiResponse<PaginatedResponse<DiamondPositionDTO>> getList(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Boolean isEnabled,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int pageSize) {
        
        DiamondPositionQuery query = DiamondPositionQuery.builder()
                .keyword(keyword)
                .isEnabled(isEnabled)
                .page(page)
                .pageSize(pageSize)
                .build();
                
        PaginatedResponse<DiamondPositionDTO> result = diamondPositionService.getList(query);
        return ApiResponse.success(result);
    }
    
    @PostMapping
    public ApiResponse<DiamondPositionDTO> create(@RequestBody @Valid CreateDiamondPositionRequest request) {
        DiamondPositionDTO result = diamondPositionService.create(request);
        return ApiResponse.success(result);
    }
    
    @PutMapping("/{id}")
    public ApiResponse<DiamondPositionDTO> update(
            @PathVariable Long id, 
            @RequestBody @Valid UpdateDiamondPositionRequest request) {
        DiamondPositionDTO result = diamondPositionService.update(id, request);
        return ApiResponse.success(result);
    }
    
    @DeleteMapping("/{id}")
    public ApiResponse<Void> delete(@PathVariable Long id) {
        diamondPositionService.delete(id);
        return ApiResponse.success(null);
    }
    
    @PatchMapping("/{id}/status")
    public ApiResponse<DiamondPositionDTO> toggleStatus(
            @PathVariable Long id,
            @RequestBody Map<String, Boolean> request) {
        Boolean isEnabled = request.get("isEnabled");
        DiamondPositionDTO result = diamondPositionService.toggleStatus(id, isEnabled);
        return ApiResponse.success(result);
    }
    
    @PutMapping("/sort-order")
    public ApiResponse<Void> updateSortOrder(@RequestBody UpdateSortOrderRequest request) {
        diamondPositionService.updateSortOrder(request.getItems());
        return ApiResponse.success(null);
    }
    
    @DeleteMapping("/batch")
    public ApiResponse<Void> batchDelete(@RequestBody Map<String, List<Long>> request) {
        List<Long> ids = request.get("ids");
        diamondPositionService.batchDelete(ids);
        return ApiResponse.success(null);
    }
    
    @PatchMapping("/batch/status")
    public ApiResponse<Void> batchUpdateStatus(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<Long> ids = (List<Long>) request.get("ids");
        Boolean isEnabled = (Boolean) request.get("isEnabled");
        diamondPositionService.batchUpdateStatus(ids, isEnabled);
        return ApiResponse.success(null);
    }
}
```

#### Service层
```java
@Service
@Transactional
public class DiamondPositionServiceImpl implements DiamondPositionService {
    
    @Autowired
    private DiamondPositionRepository repository;
    
    @Autowired
    private DiamondPositionMapper mapper;
    
    @Override
    @Transactional(readOnly = true)
    public PaginatedResponse<DiamondPositionDTO> getList(DiamondPositionQuery query) {
        Specification<DiamondPosition> spec = buildSpecification(query);
        
        Pageable pageable = PageRequest.of(
            query.getPage() - 1, 
            query.getPageSize(),
            Sort.by(Sort.Direction.ASC, "sortOrder", "id")
        );
        
        Page<DiamondPosition> page = repository.findAll(spec, pageable);
        
        List<DiamondPositionDTO> dtos = page.getContent().stream()
                .map(mapper::toDTO)
                .collect(Collectors.toList());
                
        return PaginatedResponse.<DiamondPositionDTO>builder()
                .data(dtos)
                .total(page.getTotalElements())
                .page(query.getPage())
                .pageSize(query.getPageSize())
                .totalPages(page.getTotalPages())
                .build();
    }
    
    @Override
    public DiamondPositionDTO create(CreateDiamondPositionRequest request) {
        DiamondPosition entity = mapper.toEntity(request);
        
        // 如果没有指定排序，设置为最大值+1
        if (entity.getSortOrder() == null || entity.getSortOrder() <= 0) {
            Integer maxSortOrder = repository.findMaxSortOrder();
            entity.setSortOrder(maxSortOrder != null ? maxSortOrder + 1 : 1);
        }
        
        DiamondPosition saved = repository.save(entity);
        return mapper.toDTO(saved);
    }
    
    @Override
    public DiamondPositionDTO update(Long id, UpdateDiamondPositionRequest request) {
        DiamondPosition entity = repository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("金刚位不存在: " + id));
                
        mapper.updateEntity(entity, request);
        DiamondPosition saved = repository.save(entity);
        return mapper.toDTO(saved);
    }
    
    @Override
    public void delete(Long id) {
        if (!repository.existsById(id)) {
            throw new EntityNotFoundException("金刚位不存在: " + id);
        }
        repository.deleteById(id);
    }
    
    @Override
    public DiamondPositionDTO toggleStatus(Long id, Boolean isEnabled) {
        DiamondPosition entity = repository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("金刚位不存在: " + id));
                
        entity.setIsEnabled(isEnabled);
        DiamondPosition saved = repository.save(entity);
        return mapper.toDTO(saved);
    }
    
    @Override
    public void updateSortOrder(List<UpdateSortOrderRequest> items) {
        List<DiamondPosition> entities = new ArrayList<>();
        
        for (UpdateSortOrderRequest item : items) {
            DiamondPosition entity = repository.findById(item.getId())
                    .orElseThrow(() -> new EntityNotFoundException("金刚位不存在: " + item.getId()));
            entity.setSortOrder(item.getSortOrder());
            entities.add(entity);
        }
        
        repository.saveAll(entities);
    }
    
    @Override
    public void batchDelete(List<Long> ids) {
        repository.deleteAllById(ids);
    }
    
    @Override
    public void batchUpdateStatus(List<Long> ids, Boolean isEnabled) {
        repository.batchUpdateStatus(ids, isEnabled);
    }
    
    private Specification<DiamondPosition> buildSpecification(DiamondPositionQuery query) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            if (StringUtils.hasText(query.getKeyword())) {
                String keyword = "%" + query.getKeyword() + "%";
                Predicate nameLike = criteriaBuilder.like(root.get("name"), keyword);
                Predicate descLike = criteriaBuilder.like(root.get("description"), keyword);
                predicates.add(criteriaBuilder.or(nameLike, descLike));
            }
            
            if (query.getIsEnabled() != null) {
                predicates.add(criteriaBuilder.equal(root.get("isEnabled"), query.getIsEnabled()));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
```

## 🎨 前端组件设计

### 组件架构
```
前端组件架构
├── DiamondPositionManage.vue          # 主管理界面
├── DiamondPositionDialog.vue          # 新增/编辑对话框
├── components/
│   ├── DiamondGrid.vue               # 金刚位网格组件
│   ├── DiamondItem.vue               # 金刚位单项组件
│   ├── DevicePreview.vue             # 设备预览组件
│   └── BatchOperations.vue           # 批量操作组件
├── composables/
│   ├── useDiamondPosition.ts         # 业务逻辑组合函数
│   ├── useResponsiveGrid.ts          # 响应式网格
│   ├── useDragAndDrop.ts             # 拖拽功能
│   └── useDevicePreview.ts           # 设备预览
├── types/
│   └── diamond-position.ts           # TypeScript类型定义
└── styles/
    ├── diamond-grid.scss             # 网格样式
    ├── responsive.scss               # 响应式样式
    └── animations.scss               # 动画效果
```

### 核心组件实现

#### 主管理界面组件
```vue
<template>
  <div class="diamond-manage-layout">
    <!-- 页面头部 -->
    <header class="layout-header">
      <div class="header-content">
        <h1 class="page-title">金刚位配置管理</h1>
        <div class="header-actions">
          <el-button type="primary" @click="openCreateDialog">
            <el-icon><Plus /></el-icon>
            新增金刚位
          </el-button>
        </div>
      </div>
    </header>

    <!-- 工具栏 -->
    <div class="layout-toolbar">
      <div class="toolbar-left">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索金刚位名称或描述"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="statusFilter"
          placeholder="状态筛选"
          clearable
          @change="handleSearch"
        >
          <el-option label="全部" :value="undefined" />
          <el-option label="启用" :value="true" />
          <el-option label="禁用" :value="false" />
        </el-select>
      </div>
      
      <div class="toolbar-right">
        <el-button
          v-if="selectedItems.length > 0"
          type="danger"
          @click="handleBatchDelete"
        >
          批量删除 ({{ selectedItems.length }})
        </el-button>
        
        <el-button
          v-if="selectedItems.length > 0"
          @click="handleBatchToggleStatus"
        >
          批量{{ allSelectedEnabled ? '禁用' : '启用' }}
        </el-button>
        
        <el-button @click="togglePreview">
          {{ showPreview ? '隐藏' : '显示' }}预览
        </el-button>
      </div>
    </div>

    <!-- 金刚位网格配置区 -->
    <div class="layout-grid">
      <DiamondGrid
        :items="items"
        :loading="loading"
        @item-click="handleItemClick"
        @sort-change="handleSortChange"
      />
    </div>

    <!-- 设备预览区 -->
    <div v-if="showPreview" class="layout-preview">
      <DevicePreview :items="enabledItems" />
    </div>

    <!-- 金刚位列表 -->
    <div class="layout-list">
      <el-table
        :data="items"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="名称" width="120" />
        <el-table-column prop="iconUrl" label="图标" width="80">
          <template #default="{ row }">
            <img :src="row.iconUrl" class="table-icon" :alt="row.name">
          </template>
        </el-table-column>
        <el-table-column prop="linkUrl" label="链接" show-overflow-tooltip />
        <el-table-column prop="sortOrder" label="排序" width="80" />
        <el-table-column prop="isEnabled" label="状态" width="80">
          <template #default="{ row }">
            <el-switch
              v-model="row.isEnabled"
              @change="handleToggleStatus(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column prop="updatedAt" label="更新时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.updatedAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <DiamondPositionDialog
      v-model="dialogVisible"
      :mode="dialogMode"
      :data="currentItem"
      @confirm="handleDialogConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import DiamondGrid from './components/DiamondGrid.vue'
import DevicePreview from './components/DevicePreview.vue'
import DiamondPositionDialog from './DiamondPositionDialog.vue'
import { useDiamondPosition } from './composables/useDiamondPosition'
import type { DiamondPosition } from './types/diamond-position'

// 组合式函数
const {
  items,
  loading,
  error,
  fetchItems,
  createItem,
  updateItem,
  deleteItem,
  toggleStatus,
  updateSortOrder,
  batchDelete,
  batchUpdateStatus
} = useDiamondPosition()

// 响应式数据
const searchKeyword = ref('')
const statusFilter = ref<boolean | undefined>(undefined)
const selectedItems = ref<DiamondPosition[]>([])
const showPreview = ref(true)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref<'create' | 'edit'>('create')
const currentItem = ref<DiamondPosition | null>(null)

// 计算属性
const enabledItems = computed(() => 
  items.value.filter(item => item.isEnabled)
)

const allSelectedEnabled = computed(() => 
  selectedItems.value.every(item => item.isEnabled)
)

// 事件处理
const handleSearch = async () => {
  currentPage.value = 1
  await fetchItems({
    keyword: searchKeyword.value,
    isEnabled: statusFilter.value,
    page: currentPage.value,
    pageSize: pageSize.value
  })
}

const handleItemClick = (item: DiamondPosition) => {
  handleEdit(item)
}

const handleSortChange = async (newOrder: { id: number; sortOrder: number }[]) => {
  try {
    await updateSortOrder(newOrder)
    ElMessage.success('排序更新成功')
  } catch (error) {
    ElMessage.error('排序更新失败')
  }
}

const handleSelectionChange = (selection: DiamondPosition[]) => {
  selectedItems.value = selection
}

const handleToggleStatus = async (item: DiamondPosition) => {
  try {
    await toggleStatus(item.id!, item.isEnabled)
    ElMessage.success(`${item.isEnabled ? '启用' : '禁用'}成功`)
  } catch (error) {
    ElMessage.error('状态切换失败')
  }
}

const openCreateDialog = () => {
  dialogMode.value = 'create'
  currentItem.value = null
  dialogVisible.value = true
}

const handleEdit = (item: DiamondPosition) => {
  dialogMode.value = 'edit'
  currentItem.value = { ...item }
  dialogVisible.value = true
}

const handleDelete = async (item: DiamondPosition) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除金刚位"${item.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteItem(item.id!)
    ElMessage.success('删除成功')
    await fetchItems()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedItems.value.length} 个金刚位吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedItems.value.map(item => item.id!)
    await batchDelete(ids)
    ElMessage.success('批量删除成功')
    selectedItems.value = []
    await fetchItems()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const handleBatchToggleStatus = async () => {
  try {
    const newStatus = !allSelectedEnabled.value
    const ids = selectedItems.value.map(item => item.id!)
    
    await batchUpdateStatus(ids, newStatus)
    ElMessage.success(`批量${newStatus ? '启用' : '禁用'}成功`)
    await fetchItems()
  } catch (error) {
    ElMessage.error('批量状态更新失败')
  }
}

const handleDialogConfirm = async (data: Partial<DiamondPosition>) => {
  try {
    if (dialogMode.value === 'create') {
      await createItem(data)
      ElMessage.success('创建成功')
    } else {
      await updateItem(currentItem.value!.id!, data)
      ElMessage.success('更新成功')
    }
    
    dialogVisible.value = false
    await fetchItems()
  } catch (error) {
    ElMessage.error(dialogMode.value === 'create' ? '创建失败' : '更新失败')
  }
}

const togglePreview = () => {
  showPreview.value = !showPreview.value
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchItems({
    keyword: searchKeyword.value,
    isEnabled: statusFilter.value,
    page: currentPage.value,
    pageSize: pageSize.value
  })
}

const handlePageSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchItems({
    keyword: searchKeyword.value,
    isEnabled: statusFilter.value,
    page: currentPage.value,
    pageSize: pageSize.value
  })
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  fetchItems({
    page: currentPage.value,
    pageSize: pageSize.value
  })
})
</script>

<style scoped>
.diamond-manage-layout {
  display: grid;
  grid-template-areas: 
    "header header"
    "toolbar toolbar"
    "grid preview"
    "list list";
  grid-template-rows: auto auto 1fr auto;
  grid-template-columns: 1fr 300px;
  gap: 20px;
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
}

.layout-header {
  grid-area: header;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.layout-toolbar {
  grid-area: toolbar;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.toolbar-left {
  display: flex;
  gap: 12px;
  align-items: center;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.layout-grid {
  grid-area: grid;
  overflow-y: auto;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 20px;
}

.layout-preview {
  grid-area: preview;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 16px;
  overflow-y: auto;
}

.layout-list {
  grid-area: list;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 20px;
}

.table-icon {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  object-fit: cover;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 移动端响应式 */
@media (max-width: 767px) {
  .diamond-manage-layout {
    grid-template-areas: 
      "header"
      "toolbar"
      "grid"
      "preview"
      "list";
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto auto 1fr;
    padding: 16px;
    gap: 16px;
  }
  
  .toolbar-left,
  .toolbar-right {
    flex-direction: column;
    gap: 8px;
  }
  
  .layout-toolbar {
    flex-direction: column;
    align-items: stretch;
  }
}

/* 平板端响应式 */
@media (min-width: 768px) and (max-width: 1023px) {
  .diamond-manage-layout {
    grid-template-columns: 1fr 250px;
    padding: 18px;
    gap: 18px;
  }
}
</style>
```

## 📱 响应式布局方案

### CSS Grid核心实现
```scss
// 响应式断点变量
$breakpoint-mobile: 767px;
$breakpoint-tablet: 1023px;
$breakpoint-desktop: 1439px;

// 金刚位网格容器
.diamond-grid {
  display: grid;
  gap: var(--grid-gap);
  padding: var(--grid-padding);
  width: 100%;
  box-sizing: border-box;
  
  // 默认移动端布局
  grid-template-columns: repeat(4, 1fr);
  --grid-gap: 12px;
  --grid-padding: 16px;
  --item-size: 48px;
  
  // 平板端布局
  @media (min-width: 768px) {
    grid-template-columns: repeat(6, 1fr);
    --grid-gap: 16px;
    --grid-padding: 20px;
    --item-size: 56px;
  }
  
  // 桌面端布局
  @media (min-width: 1024px) {
    grid-template-columns: repeat(8, 1fr);
    --grid-gap: 20px;
    --grid-padding: 24px;
    --item-size: 64px;
  }
  
  // 大屏桌面端布局
  @media (min-width: 1440px) {
    grid-template-columns: repeat(10, 1fr);
    --grid-gap: 24px;
    --grid-padding: 28px;
    --item-size: 72px;
  }
}

// 金刚位单项样式
.diamond-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 12px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  min-height: calc(var(--item-size) + 32px);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
  }
  
  &.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
    z-index: 1000;
  }
  
  &.drag-over {
    border-color: #10b981;
    background-color: #f0fdf4;
  }
}

.diamond-icon {
  width: var(--item-size);
  height: var(--item-size);
  border-radius: 8px;
  object-fit: cover;
  margin-bottom: 4px;
}

.diamond-title {
  font-size: 12px;
  color: #374151;
  text-align: center;
  line-height: 1.2;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  
  @media (min-width: 768px) {
    font-size: 13px;
  }
  
  @media (min-width: 1024px) {
    font-size: 14px;
  }
}
```

### 响应式组合函数
```typescript
import { ref, computed, onMounted, onUnmounted } from 'vue'

export function useResponsiveGrid() {
  const screenWidth = ref(window.innerWidth)
  
  const updateScreenWidth = () => {
    screenWidth.value = window.innerWidth
  }
  
  const gridColumns = computed(() => {
    if (screenWidth.value < 768) return 4
    if (screenWidth.value < 1024) return 6
    if (screenWidth.value < 1440) return 8
    return 10
  })
  
  const deviceType = computed(() => {
    if (screenWidth.value < 768) return 'mobile'
    if (screenWidth.value < 1024) return 'tablet'
    return 'desktop'
  })
  
  const itemSize = computed(() => {
    if (screenWidth.value < 768) return 48
    if (screenWidth.value < 1024) return 56
    if (screenWidth.value < 1440) return 64
    return 72
  })
  
  const gridGap = computed(() => {
    if (screenWidth.value < 768) return 12
    if (screenWidth.value < 1024) return 16
    if (screenWidth.value < 1440) return 20
    return 24
  })
  
  onMounted(() => {
    window.addEventListener('resize', updateScreenWidth)
  })
  
  onUnmounted(() => {
    window.removeEventListener('resize', updateScreenWidth)
  })
  
  return {
    screenWidth,
    gridColumns,
    deviceType,
    itemSize,
    gridGap
  }
}
```

## 🎯 拖拽功能实现

### 拖拽组合函数
```typescript
import { ref, nextTick } from 'vue'
import type { DiamondPosition } from '../types/diamond-position'

export function useDragAndDrop(
  items: Ref<DiamondPosition[]>,
  onSortChange: (newOrder: { id: number; sortOrder: number }[]) => void
) {
  const draggedItem = ref<DiamondPosition | null>(null)
  const dragOverItem = ref<DiamondPosition | null>(null)
  const isDragging = ref(false)
  
  const handleDragStart = (event: DragEvent, item: DiamondPosition) => {
    draggedItem.value = item
    isDragging.value = true
    
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'move'
      event.dataTransfer.setData('text/html', '')
    }
    
    // 添加拖拽样式
    const target = event.target as HTMLElement
    target.classList.add('dragging')
  }
  
  const handleDragOver = (event: DragEvent) => {
    event.preventDefault()
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move'
    }
  }
  
  const handleDragEnter = (event: DragEvent, item: DiamondPosition) => {
    event.preventDefault()
    
    if (draggedItem.value && draggedItem.value.id !== item.id) {
      dragOverItem.value = item
      
      const target = event.target as HTMLElement
      const itemElement = target.closest('.diamond-item') as HTMLElement
      if (itemElement) {
        itemElement.classList.add('drag-over')
      }
    }
  }
  
  const handleDragLeave = (event: DragEvent) => {
    const target = event.target as HTMLElement
    const itemElement = target.closest('.diamond-item') as HTMLElement
    if (itemElement) {
      itemElement.classList.remove('drag-over')
    }
  }
  
  const handleDrop = async (event: DragEvent, targetItem: DiamondPosition) => {
    event.preventDefault()
    
    if (!draggedItem.value || draggedItem.value.id === targetItem.id) {
      return
    }
    
    // 计算新的排序
    const newOrder = calculateNewOrder(draggedItem.value, targetItem)
    
    // 更新本地状态
    updateLocalOrder(newOrder)
    
    // 调用回调函数
    onSortChange(newOrder)
    
    // 清理状态
    cleanupDragState()
  }
  
  const handleDragEnd = (event: DragEvent) => {
    cleanupDragState()
    
    // 移除所有拖拽相关的样式
    const allItems = document.querySelectorAll('.diamond-item')
    allItems.forEach(item => {
      item.classList.remove('dragging', 'drag-over')
    })
  }
  
  const calculateNewOrder = (
    draggedItem: DiamondPosition,
    targetItem: DiamondPosition
  ): { id: number; sortOrder: number }[] => {
    const currentItems = [...items.value]
    const draggedIndex = currentItems.findIndex(item => item.id === draggedItem.id)
    const targetIndex = currentItems.findIndex(item => item.id === targetItem.id)
    
    // 移除拖拽的项目
    const [removed] = currentItems.splice(draggedIndex, 1)
    
    // 插入到目标位置
    currentItems.splice(targetIndex, 0, removed)
    
    // 重新计算排序值
    return currentItems.map((item, index) => ({
      id: item.id!,
      sortOrder: index + 1
    }))
  }
  
  const updateLocalOrder = (newOrder: { id: number; sortOrder: number }[]) => {
    const orderMap = new Map(newOrder.map(item => [item.id, item.sortOrder]))
    
    items.value.forEach(item => {
      const newSortOrder = orderMap.get(item.id!)
      if (newSortOrder !== undefined) {
        item.sortOrder = newSortOrder
      }
    })
    
    // 重新排序
    items.value.sort((a, b) => a.sortOrder - b.sortOrder)
  }
  
  const cleanupDragState = () => {
    draggedItem.value = null
    dragOverItem.value = null
    isDragging.value = false
  }
  
  return {
    draggedItem,
    dragOverItem,
    isDragging,
    handleDragStart,
    handleDragOver,
    handleDragEnter,
    handleDragLeave,
    handleDrop,
    handleDragEnd
  }
}
```

## 🚀 部署方案

### 前端部署

#### 1. 构建配置 (vite.config.ts)
```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    rollupOptions: {
      output: {
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
      }
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  }
})
```

#### 2. Nginx配置
```nginx
server {
    listen 80;
    server_name diamond-admin.example.com;
    
    root /var/www/diamond-admin/dist;
    index index.html;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### 后端部署

#### 1. Docker配置
```dockerfile
# Dockerfile
FROM openjdk:17-jdk-slim

VOLUME /tmp

COPY target/diamond-position-admin-*.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

#### 2. Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: diamond-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: diamond_admin
      MYSQL_USER: diamond
      MYSQL_PASSWORD: diamond123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password

  app:
    build: .
    container_name: diamond-app
    ports:
      - "8080:8080"
    environment:
      SPRING_DATASOURCE_URL: *********************************************************************
      SPRING_DATASOURCE_USERNAME: diamond
      SPRING_DATASOURCE_PASSWORD: diamond123
    depends_on:
      - mysql
    restart: unless-stopped

volumes:
  mysql_data:
```

#### 3. 生产环境配置
```yaml
# application-prod.yml
spring:
  datasource:
    url: ************************************************************************
    username: ${DB_USERNAME:diamond}
    password: ${DB_PASSWORD:diamond123}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        format_sql: false
        use_sql_comments: false
  
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

logging:
  level:
    com.example.diamond: INFO
    org.springframework.web: WARN
  file:
    name: logs/diamond-admin.log
  logback:
    rollingpolicy:
      max-file-size: 100MB
      max-history: 30

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
```

## 🧪 测试方案

### 前端测试

#### 1. 单元测试 (Vitest)
```typescript
// tests/components/DiamondGrid.test.ts
import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import DiamondGrid from '@/components/DiamondGrid.vue'
import type { DiamondPosition } from '@/types/diamond-position'

const mockItems: DiamondPosition[] = [
  {
    id: 1,
    name: '扫一扫',
    iconUrl: 'https://example.com/scan.png',
    linkUrl: '/scan',
    sortOrder: 1,
    isEnabled: true,
    description: '扫码功能',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
]

describe('DiamondGrid', () => {
  it('renders items correctly', () => {
    const wrapper = mount(DiamondGrid, {
      props: {
        items: mockItems,
        loading: false
      }
    })
    
    expect(wrapper.find('.diamond-grid').exists()).toBe(true)
    expect(wrapper.find('.diamond-item').exists()).toBe(true)
    expect(wrapper.text()).toContain('扫一扫')
  })
  
  it('emits item-click event when item is clicked', async () => {
    const wrapper = mount(DiamondGrid, {
      props: {
        items: mockItems,
        loading: false
      }
    })
    
    await wrapper.find('.diamond-item').trigger('click')
    
    expect(wrapper.emitted('item-click')).toBeTruthy()
    expect(wrapper.emitted('item-click')![0]).toEqual([mockItems[0]])
  })
  
  it('shows loading state', () => {
    const wrapper = mount(DiamondGrid, {
      props: {
        items: [],
        loading: true
      }
    })
    
    expect(wrapper.find('.loading').exists()).toBe(true)
  })
})
```

#### 2. E2E测试 (Cypress)
```typescript
// cypress/e2e/diamond-management.cy.ts
describe('Diamond Position Management', () => {
  beforeEach(() => {
    cy.visit('/diamond-management')
  })
  
  it('should display diamond grid', () => {
    cy.get('.diamond-grid').should('be.visible')
    cy.get('.diamond-item').should('have.length.greaterThan', 0)
  })
  
  it('should create new diamond position', () => {
    cy.get('[data-testid="create-button"]').click()
    cy.get('[data-testid="dialog"]').should('be.visible')
    
    cy.get('[data-testid="name-input"]').type('新功能')
    cy.get('[data-testid="link-input"]').type('/new-feature')
    cy.get('[data-testid="description-input"]').type('新功能描述')
    
    cy.get('[data-testid="confirm-button"]').click()
    
    cy.get('.el-message--success').should('be.visible')
    cy.contains('新功能').should('be.visible')
  })
  
  it('should support drag and drop sorting', () => {
    cy.get('.diamond-item').first().as('firstItem')
    cy.get('.diamond-item').last().as('lastItem')
    
    cy.get('@firstItem').trigger('dragstart')
    cy.get('@lastItem').trigger('dragover')
    cy.get('@lastItem').trigger('drop')
    
    cy.get('.el-message--success').should('contain', '排序更新成功')
  })
  
  it('should filter items by search keyword', () => {
    cy.get('[data-testid="search-input"]').type('扫一扫')
    
    cy.get('.diamond-item').should('have.length', 1)
    cy.contains('扫一扫').should('be.visible')
  })
  
  it('should toggle item status', () => {
    cy.get('.diamond-item').first().within(() => {
      cy.get('.el-switch').click()
    })
    
    cy.get('.el-message--success').should('be.visible')
  })
})
```

### 后端测试

#### 1. 单元测试 (JUnit 5)
```java
@ExtendWith(MockitoExtension.class)
class DiamondPositionServiceTest {
    
    @Mock
    private DiamondPositionRepository repository;
    
    @Mock
    private DiamondPositionMapper mapper;
    
    @InjectMocks
    private DiamondPositionServiceImpl service;
    
    @Test
    void shouldCreateDiamondPosition() {
        // Given
        CreateDiamondPositionRequest request = CreateDiamondPositionRequest.builder()
                .name("测试功能")
                .iconUrl("https://example.com/test.png")
                .linkUrl("/test")
                .description("测试描述")
                .build();
                
        DiamondPosition entity = new DiamondPosition();
        entity.setName("测试功能");
        
        DiamondPosition savedEntity = new DiamondPosition();
        savedEntity.setId(1L);
        savedEntity.setName("测试功能");
        
        DiamondPositionDTO expectedDto = DiamondPositionDTO.builder()
                .id(1L)
                .name("测试功能")
                .build();
        
        when(mapper.toEntity(request)).thenReturn(entity);
        when(repository.findMaxSortOrder()).thenReturn(5);
        when(repository.save(entity)).thenReturn(savedEntity);
        when(mapper.toDTO(savedEntity)).thenReturn(expectedDto);
        
        // When
        DiamondPositionDTO result = service.create(request);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getName()).isEqualTo("测试功能");
        assertThat(entity.getSortOrder()).isEqualTo(6);
        
        verify(repository).save(entity);
    }
    
    @Test
    void shouldThrowExceptionWhenDiamondPositionNotFound() {
        // Given
        Long id = 999L;
        when(repository.findById(id)).thenReturn(Optional.empty());
        
        // When & Then
        assertThatThrownBy(() -> service.update(id, new UpdateDiamondPositionRequest()))
                .isInstanceOf(EntityNotFoundException.class)
                .hasMessage("金刚位不存在: " + id);
    }
    
    @Test
    void shouldUpdateSortOrder() {
        // Given
        List<UpdateSortOrderRequest> requests = Arrays.asList(
                new UpdateSortOrderRequest(1L, 2),
                new UpdateSortOrderRequest(2L, 1)
        );
        
        DiamondPosition entity1 = new DiamondPosition();
        entity1.setId(1L);
        DiamondPosition entity2 = new DiamondPosition();
        entity2.setId(2L);
        
        when(repository.findById(1L)).thenReturn(Optional.of(entity1));
        when(repository.findById(2L)).thenReturn(Optional.of(entity2));
        
        // When
        service.updateSortOrder(requests);
        
        // Then
        assertThat(entity1.getSortOrder()).isEqualTo(2);
        assertThat(entity2.getSortOrder()).isEqualTo(1);
        verify(repository).saveAll(Arrays.asList(entity1, entity2));
    }
}
```

#### 2. 集成测试
```java
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestPropertySource(locations = "classpath:application-test.properties")
class DiamondPositionIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Autowired
    private DiamondPositionRepository repository;
    
    @Test
    void shouldCreateAndRetrieveDiamondPosition() {
        // Given
        CreateDiamondPositionRequest request = CreateDiamondPositionRequest.builder()
                .name("集成测试")
                .iconUrl("https://example.com/test.png")
                .linkUrl("/test")
                .description("集成测试描述")
                .build();
        
        // When - Create
        ResponseEntity<ApiResponse> createResponse = restTemplate.postForEntity(
                "/api/diamond-positions", request, ApiResponse.class);
        
        // Then - Create
        assertThat(createResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(createResponse.getBody().isSuccess()).isTrue();
        
        // When - Retrieve
        ResponseEntity<ApiResponse> getResponse = restTemplate.getForEntity(
                "/api/diamond-positions", ApiResponse.class);
        
        // Then - Retrieve
        assertThat(getResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(getResponse.getBody().isSuccess()).isTrue();
    }
}
```

## 🚀 性能优化

### 前端性能优化

#### 1. 代码分割和懒加载
```typescript
// router/index.ts
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/diamond-management',
      name: 'DiamondManagement',
      component: () => import('@/views/DiamondPositionManage.vue'),
      meta: { title: '金刚位配置管理' }
    }
  ]
})

export default router
```

#### 2. 虚拟滚动优化
```vue
<!-- components/VirtualList.vue -->
<template>
  <div class="virtual-list" ref="containerRef" @scroll="handleScroll">
    <div class="virtual-list-phantom" :style="{ height: totalHeight + 'px' }"></div>
    <div class="virtual-list-content" :style="{ transform: `translateY(${startOffset}px)` }">
      <div
        v-for="item in visibleItems"
        :key="item.id"
        class="virtual-list-item"
        :style="{ height: itemHeight + 'px' }"
      >
        <slot :item="item" :index="item.index"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface Props {
  items: any[]
  itemHeight: number
  containerHeight: number
}

const props = defineProps<Props>()

const containerRef = ref<HTMLElement>()
const scrollTop = ref(0)
const visibleCount = computed(() => Math.ceil(props.containerHeight / props.itemHeight) + 2)
const startIndex = computed(() => Math.floor(scrollTop.value / props.itemHeight))
const endIndex = computed(() => Math.min(startIndex.value + visibleCount.value, props.items.length))
const startOffset = computed(() => startIndex.value * props.itemHeight)
const totalHeight = computed(() => props.items.length * props.itemHeight)

const visibleItems = computed(() => {
  return props.items.slice(startIndex.value, endIndex.value).map((item, index) => ({
    ...item,
    index: startIndex.value + index
  }))
})

const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement
  scrollTop.value = target.scrollTop
}

onMounted(() => {
  if (containerRef.value) {
    containerRef.value.style.height = props.containerHeight + 'px'
  }
})
</script>
```

#### 3. 图片懒加载
```typescript
// composables/useLazyLoad.ts
import { ref, onMounted, onUnmounted } from 'vue'

export function useLazyLoad() {
  const observer = ref<IntersectionObserver | null>(null)
  
  const createObserver = () => {
    observer.value = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            const src = img.dataset.src
            if (src) {
              img.src = src
              img.removeAttribute('data-src')
              observer.value?.unobserve(img)
            }
          }
        })
      },
      {
        rootMargin: '50px'
      }
    )
  }
  
  const observeImage = (img: HTMLImageElement) => {
    observer.value?.observe(img)
  }
  
  onMounted(() => {
    createObserver()
  })
  
  onUnmounted(() => {
    observer.value?.disconnect()
  })
  
  return {
    observeImage
  }
}
```

### 后端性能优化

#### 1. 数据库查询优化
```java
@Repository
public interface DiamondPositionRepository extends JpaRepository<DiamondPosition, Long> {
    
    @Query("SELECT dp FROM DiamondPosition dp WHERE dp.isEnabled = true ORDER BY dp.sortOrder ASC")
    List<DiamondPosition> findEnabledOrderBySortOrder();
    
    @Query("SELECT MAX(dp.sortOrder) FROM DiamondPosition dp")
    Integer findMaxSortOrder();
    
    @Modifying
    @Query("UPDATE DiamondPosition dp SET dp.isEnabled = :isEnabled WHERE dp.id IN :ids")
    void batchUpdateStatus(@Param("ids") List<Long> ids, @Param("isEnabled") Boolean isEnabled);
    
    @Query(value = "SELECT * FROM diamond_position WHERE " +
           "(:keyword IS NULL OR name LIKE CONCAT('%', :keyword, '%') OR description LIKE CONCAT('%', :keyword, '%')) " +
           "AND (:isEnabled IS NULL OR is_enabled = :isEnabled) " +
           "ORDER BY sort_order ASC, id ASC " +
           "LIMIT :offset, :limit", nativeQuery = true)
    List<DiamondPosition> findWithFilters(
            @Param("keyword") String keyword,
            @Param("isEnabled") Boolean isEnabled,
            @Param("offset") int offset,
            @Param("limit") int limit
    );
}
```

#### 2. Redis缓存策略
```java
@Service
@CacheConfig(cacheNames = "diamond-position")
public class DiamondPositionCacheService {
    
    @Autowired
    private DiamondPositionRepository repository;
    
    @Cacheable(key = "'enabled-list'")
    public List<DiamondPositionDTO> getEnabledList() {
        List<DiamondPosition> entities = repository.findEnabledOrderBySortOrder();
        return entities.stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }
    
    @CacheEvict(key = "'enabled-list'")
    public void evictEnabledListCache() {
        // 缓存失效
    }
    
    @CacheEvict(allEntries = true)
    public void evictAllCache() {
        // 清空所有缓存
    }
}
```

## 📊 监控方案

### 应用监控

#### 1. Spring Boot Actuator配置
```yaml
# application.yml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
    metrics:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
```

#### 2. 自定义监控指标
```java
@Component
public class DiamondPositionMetrics {
    
    private final Counter createCounter;
    private final Counter updateCounter;
    private final Counter deleteCounter;
    private final Timer queryTimer;
    
    public DiamondPositionMetrics(MeterRegistry meterRegistry) {
        this.createCounter = Counter.builder("diamond.position.create")
                .description("Number of diamond positions created")
                .register(meterRegistry);
                
        this.updateCounter = Counter.builder("diamond.position.update")
                .description("Number of diamond positions updated")
                .register(meterRegistry);
                
        this.deleteCounter = Counter.builder("diamond.position.delete")
                .description("Number of diamond positions deleted")
                .register(meterRegistry);
                
        this.queryTimer = Timer.builder("diamond.position.query")
                .description("Time taken to query diamond positions")
                .register(meterRegistry);
    }
    
    public void incrementCreate() {
        createCounter.increment();
    }
    
    public void incrementUpdate() {
        updateCounter.increment();
    }
    
    public void incrementDelete() {
        deleteCounter.increment();
    }
    
    public Timer.Sample startQueryTimer() {
        return Timer.start();
    }
    
    public void recordQueryTime(Timer.Sample sample) {
        sample.stop(queryTimer);
    }
}
```

### 前端监控

#### 1. 性能监控
```typescript
// utils/performance.ts
export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  
  static getInstance(): PerformanceMonitor {
    if (!this.instance) {
      this.instance = new PerformanceMonitor()
    }
    return this.instance
  }
  
  // 页面加载时间监控
  measurePageLoad(): void {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      const loadTime = navigation.loadEventEnd - navigation.fetchStart
      
      this.sendMetric('page_load_time', loadTime)
    })
  }
  
  // API请求时间监控
  measureApiCall(url: string, startTime: number): void {
    const endTime = performance.now()
    const duration = endTime - startTime
    
    this.sendMetric('api_call_duration', duration, { url })
  }
  
  // 组件渲染时间监控
  measureComponentRender(componentName: string, renderTime: number): void {
    this.sendMetric('component_render_time', renderTime, { component: componentName })
  }
  
  private sendMetric(name: string, value: number, tags?: Record<string, string>): void {
    // 发送监控数据到监控系统
    console.log(`Metric: ${name}, Value: ${value}, Tags:`, tags)
  }
}
```

## 🔒 安全方案

### 前端安全

#### 1. XSS防护
```typescript
// utils/security.ts
export function sanitizeHtml(html: string): string {
  const div = document.createElement('div')
  div.textContent = html
  return div.innerHTML
}

export function escapeHtml(text: string): string {
  const div = document.createElement('div')
  div.appendChild(document.createTextNode(text))
  return div.innerHTML
}
```

#### 2. CSRF防护
```typescript
// api/request.ts
import axios from 'axios'

const apiClient = axios.create({
  baseURL: '/api',
  timeout: 10000,
  withCredentials: true
})

// 请求拦截器 - 添加CSRF Token
apiClient.interceptors.request.use(
  (config) => {
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
    if (csrfToken) {
      config.headers['X-CSRF-TOKEN'] = csrfToken
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)
```

### 后端安全

#### 1. Spring Security配置
```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .csrf(csrf -> csrf
                .csrfTokenRepository(CookieCsrfTokenRepository.withHttpOnlyFalse())
                .ignoringRequestMatchers("/api/public/**")
            )
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            )
            .authorizeHttpRequests(auth -> auth
                .requestMatchers("/api/public/**").permitAll()
                .requestMatchers("/api/diamond-positions/**").hasRole("ADMIN")
                .anyRequest().authenticated()
            )
            .oauth2ResourceServer(oauth2 -> oauth2
                .jwt(jwt -> jwt.jwtDecoder(jwtDecoder()))
            );
            
        return http.build();
    }
    
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("http://localhost:*", "https://*.example.com"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "PATCH"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/api/**", configuration);
        return source;
    }
}
```

#### 2. 输入验证
```java
@RestController
@Validated
public class DiamondPositionController {
    
    @PostMapping
    public ApiResponse<DiamondPositionDTO> create(
            @RequestBody @Valid CreateDiamondPositionRequest request) {
        // 业务逻辑
    }
}

@Data
@Builder
public class CreateDiamondPositionRequest {
    
    @NotBlank(message = "名称不能为空")
    @Size(max = 50, message = "名称长度不能超过50个字符")
    private String name;
    
    @NotBlank(message = "图标URL不能为空")
    @URL(message = "图标URL格式不正确")
    @Size(max = 500, message = "图标URL长度不能超过500个字符")
    private String iconUrl;
    
    @NotBlank(message = "链接URL不能为空")
    @Size(max = 500, message = "链接URL长度不能超过500个字符")
    private String linkUrl;
    
    @Min(value = 1, message = "排序权重必须大于0")
    private Integer sortOrder;
    
    @Size(max = 200, message = "描述长度不能超过200个字符")
    private String description;
}
```

## 📈 扩展规划

### 功能扩展

1. **多租户支持**: 支持不同租户的金刚位配置隔离
2. **A/B测试**: 支持金刚位配置的A/B测试功能
3. **个性化推荐**: 基于用户行为的金刚位个性化排序
4. **数据分析**: 金刚位点击率、转化率等数据分析
5. **国际化**: 支持多语言金刚位配置

### 技术升级

1. **微服务架构**: 拆分为独立的微服务
2. **容器化部署**: 使用Kubernetes进行容器编排
3. **服务网格**: 使用Istio进行服务治理
4. **事件驱动**: 使用消息队列实现事件驱动架构
5. **GraphQL**: 提供GraphQL API支持

## 📝 总结

本技术文档详细描述了金刚位配置管理系统的完整技术实现方案，涵盖了从数据库设计到前端组件、从API接口到部署方案的全方位技术内容。

### 技术亮点

1. **极简架构**: 单表设计，降低系统复杂度
2. **响应式设计**: CSS Grid + Vue 3组合，支持多端适配
3. **拖拽交互**: 直观的拖拽排序功能
4. **性能优化**: 虚拟滚动、懒加载、缓存策略
5. **安全保障**: XSS/CSRF防护、输入验证、权限控制
6. **监控完善**: 应用监控、性能监控、错误追踪

### 开发建议

1. **渐进式开发**: 按优先级逐步实现功能
2. **测试驱动**: 编写充分的单元测试和集成测试
3. **代码规范**: 遵循团队代码规范和最佳实践
4. **文档维护**: 及时更新技术文档和API文档
5. **性能监控**: 持续关注系统性能和用户体验

通过本技术方案的实施，可以构建一个高性能、易维护、用户体验优秀的金刚位配置管理系统。