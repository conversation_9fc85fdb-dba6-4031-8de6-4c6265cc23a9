-- 金刚位配置表结构优化迁移脚本
-- 基于现有表结构，增加图标类型支持和其他功能增强

-- 1. 检查现有表结构
-- 假设现有表名为 grid_item，需要重构为支持多种图标类型

-- 2. 创建新的金刚位配置表（如果不存在）
CREATE TABLE IF NOT EXISTS `oto_diamond_position` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '金刚位名称',
  `icon` text DEFAULT NULL COMMENT '图标内容（URL、SVG代码、Base64等）',
  `icon_type` varchar(20) DEFAULT 'url' COMMENT '图标类型：url-链接地址，svg-SVG代码，base64-Base64编码，emoji-表情符号',
  `url` varchar(500) DEFAULT NULL COMMENT '跳转链接',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序序号',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `platform` varchar(20) DEFAULT 'ALL' COMMENT '平台：ALL-全平台，IOS-iOS，ANDROID-Android，H5-H5',
  `group_id` bigint(20) DEFAULT NULL COMMENT '分组ID',
  `is_hot` tinyint(1) DEFAULT 0 COMMENT '是否热门：0-否，1-是',
  `is_new` tinyint(1) DEFAULT 0 COMMENT '是否新品：0-否，1-是',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  PRIMARY KEY (`id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`),
  KEY `idx_icon_type` (`icon_type`),
  KEY `idx_platform` (`platform`),
  KEY `idx_group_id` (`group_id`),
  KEY `idx_created_time` (`created_time`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='金刚位配置表';

-- 3. 数据迁移脚本（从现有 grid_item 表迁移数据）
-- 注意：这个脚本需要根据实际的现有表结构进行调整

INSERT INTO `oto_diamond_position` (
  `name`, 
  `icon`, 
  `icon_type`, 
  `url`, 
  `sort_order`, 
  `status`, 
  `description`, 
  `platform`, 
  `group_id`, 
  `is_hot`, 
  `is_new`, 
  `created_time`, 
  `updated_time`, 
  `created_by`, 
  `updated_by`
)
SELECT 
  `title` as `name`,
  `icon` as `icon`,
  CASE 
    WHEN `icon_type` IS NOT NULL THEN `icon_type`
    WHEN `icon` LIKE 'http%' THEN 'url'
    WHEN `icon` LIKE '<svg%' THEN 'svg'
    WHEN `icon` LIKE 'data:image%' THEN 'base64'
    ELSE 'url'
  END as `icon_type`,
  COALESCE(`route`, `jump_url`) as `url`,
  COALESCE(`sort_order`, `sort`, 0) as `sort_order`,
  CASE 
    WHEN `is_active` = '1' THEN 1
    WHEN `status` = '1' THEN 1
    ELSE 0
  END as `status`,
  `description`,
  COALESCE(`platform`, 'ALL') as `platform`,
  `group_id`,
  CASE WHEN `is_hot` = '1' THEN 1 ELSE 0 END as `is_hot`,
  CASE WHEN `is_new` = '1' THEN 1 ELSE 0 END as `is_new`,
  COALESCE(`create_time`, NOW()) as `created_time`,
  COALESCE(`update_time`, NOW()) as `updated_time`,
  `create_by` as `created_by`,
  `update_by` as `updated_by`
FROM `grid_item` 
WHERE `del_flag` = '0' OR `del_flag` IS NULL;

-- 4. 创建图标类型验证触发器
DELIMITER $$

CREATE TRIGGER `validate_icon_type_before_insert` 
BEFORE INSERT ON `oto_diamond_position`
FOR EACH ROW
BEGIN
  -- 自动检测图标类型
  IF NEW.icon_type IS NULL OR NEW.icon_type = '' THEN
    IF NEW.icon LIKE 'http%' OR NEW.icon LIKE '/%' THEN
      SET NEW.icon_type = 'url';
    ELSEIF NEW.icon LIKE '<svg%' AND NEW.icon LIKE '%</svg>%' THEN
      SET NEW.icon_type = 'svg';
    ELSEIF NEW.icon LIKE 'data:image%' THEN
      SET NEW.icon_type = 'base64';
    ELSEIF CHAR_LENGTH(NEW.icon) <= 10 THEN
      SET NEW.icon_type = 'emoji';
    ELSE
      SET NEW.icon_type = 'url';
    END IF;
  END IF;
  
  -- 验证图标类型和内容的匹配性
  IF NEW.icon_type = 'url' AND NEW.icon NOT LIKE 'http%' AND NEW.icon NOT LIKE '/%' THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'URL类型图标必须以http://、https://或/开头';
  END IF;
  
  IF NEW.icon_type = 'svg' AND (NEW.icon NOT LIKE '<svg%' OR NEW.icon NOT LIKE '%</svg>%') THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'SVG类型图标必须以<svg开头并以</svg>结尾';
  END IF;
  
  IF NEW.icon_type = 'base64' AND NEW.icon NOT LIKE 'data:image%' THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Base64类型图标必须以data:image/开头';
  END IF;
  
  IF NEW.icon_type = 'emoji' AND CHAR_LENGTH(NEW.icon) > 10 THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '表情符号类型图标长度不能超过10个字符';
  END IF;
END$$

CREATE TRIGGER `validate_icon_type_before_update` 
BEFORE UPDATE ON `oto_diamond_position`
FOR EACH ROW
BEGIN
  -- 自动检测图标类型
  IF NEW.icon_type IS NULL OR NEW.icon_type = '' THEN
    IF NEW.icon LIKE 'http%' OR NEW.icon LIKE '/%' THEN
      SET NEW.icon_type = 'url';
    ELSEIF NEW.icon LIKE '<svg%' AND NEW.icon LIKE '%</svg>%' THEN
      SET NEW.icon_type = 'svg';
    ELSEIF NEW.icon LIKE 'data:image%' THEN
      SET NEW.icon_type = 'base64';
    ELSEIF CHAR_LENGTH(NEW.icon) <= 10 THEN
      SET NEW.icon_type = 'emoji';
    ELSE
      SET NEW.icon_type = 'url';
    END IF;
  END IF;
  
  -- 验证图标类型和内容的匹配性
  IF NEW.icon_type = 'url' AND NEW.icon NOT LIKE 'http%' AND NEW.icon NOT LIKE '/%' THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'URL类型图标必须以http://、https://或/开头';
  END IF;
  
  IF NEW.icon_type = 'svg' AND (NEW.icon NOT LIKE '<svg%' OR NEW.icon NOT LIKE '%</svg>%') THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'SVG类型图标必须以<svg开头并以</svg>结尾';
  END IF;
  
  IF NEW.icon_type = 'base64' AND NEW.icon NOT LIKE 'data:image%' THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Base64类型图标必须以data:image/开头';
  END IF;
  
  IF NEW.icon_type = 'emoji' AND CHAR_LENGTH(NEW.icon) > 10 THEN
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '表情符号类型图标长度不能超过10个字符';
  END IF;
END$$

DELIMITER ;

-- 5. 插入示例数据
INSERT INTO `oto_diamond_position` (`name`, `icon`, `icon_type`, `url`, `sort_order`, `status`, `description`, `platform`) VALUES
('扫一扫', 'https://example.com/icons/scan.png', 'url', '/scan', 1, 1, '扫码功能', 'ALL'),
('付款码', '💰', 'emoji', '/payment', 2, 1, '付款码功能', 'ALL'),
('充值', '<svg viewBox="0 0 24 24"><path d="M12 2L2 7v10c0 5.55 3.84 10 9 10s9-4.45 9-10V7l-10-5z"/></svg>', 'svg', '/recharge', 3, 1, '账户充值', 'ALL'),
('转账', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64', '/transfer', 4, 0, '转账功能', 'ALL'),
('理财', 'https://example.com/icons/finance.png', 'url', '/finance', 5, 1, '理财产品', 'ALL'),
('信用卡', '💳', 'emoji', '/credit-card', 6, 1, '信用卡服务', 'ALL'),
('保洁服务', 'https://example.com/icons/cleaning.png', 'url', '/pages/service/cleaning', 7, 1, '家庭保洁服务', 'ALL'),
('维修服务', '🔧', 'emoji', '/pages/service/repair', 8, 1, '家电维修服务', 'ALL');

-- 6. 创建视图以兼容现有代码
CREATE OR REPLACE VIEW `grid_item_view` AS
SELECT 
  `id`,
  `name` as `title`,
  `icon`,
  `icon_type`,
  `url` as `route`,
  `url` as `jump_url`,
  `sort_order`,
  CASE WHEN `status` = 1 THEN '1' ELSE '0' END as `is_active`,
  `description`,
  `platform`,
  `group_id`,
  CASE WHEN `is_hot` = 1 THEN '1' ELSE '0' END as `is_hot`,
  CASE WHEN `is_new` = 1 THEN '1' ELSE '0' END as `is_new`,
  `created_time` as `create_time`,
  `updated_time` as `update_time`,
  `created_by` as `create_by`,
  `updated_by` as `update_by`,
  `del_flag`
FROM `oto_diamond_position`
WHERE `del_flag` = '0';

-- 7. 创建常用查询的存储过程
DELIMITER $$

CREATE PROCEDURE `GetDiamondPositionsByPlatform`(
  IN p_platform VARCHAR(20),
  IN p_status TINYINT
)
BEGIN
  SELECT * FROM `oto_diamond_position`
  WHERE `del_flag` = '0'
    AND (`platform` = p_platform OR `platform` = 'ALL' OR p_platform = 'ALL')
    AND (`status` = p_status OR p_status IS NULL)
  ORDER BY `sort_order` ASC, `id` ASC;
END$$

CREATE PROCEDURE `UpdateDiamondPositionSort`(
  IN p_items JSON
)
BEGIN
  DECLARE i INT DEFAULT 0;
  DECLARE item_count INT;
  DECLARE item_id BIGINT;
  DECLARE item_sort INT;
  
  SET item_count = JSON_LENGTH(p_items);
  
  WHILE i < item_count DO
    SET item_id = JSON_UNQUOTE(JSON_EXTRACT(p_items, CONCAT('$[', i, '].id')));
    SET item_sort = JSON_UNQUOTE(JSON_EXTRACT(p_items, CONCAT('$[', i, '].sortOrder')));
    
    UPDATE `oto_diamond_position` 
    SET `sort_order` = item_sort, `updated_time` = NOW()
    WHERE `id` = item_id AND `del_flag` = '0';
    
    SET i = i + 1;
  END WHILE;
END$$

DELIMITER ;

-- 8. 创建性能优化索引
CREATE INDEX `idx_platform_status_sort` ON `oto_diamond_position` (`platform`, `status`, `sort_order`);
CREATE INDEX `idx_group_status_sort` ON `oto_diamond_position` (`group_id`, `status`, `sort_order`);

-- 9. 添加表注释和字段注释
ALTER TABLE `oto_diamond_position` COMMENT = '金刚位配置表 - 支持多种图标类型的金刚位管理';

-- 完成迁移
SELECT 'Diamond Position table migration completed successfully!' as message;
