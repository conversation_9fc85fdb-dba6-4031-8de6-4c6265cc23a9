# 金刚位配置管理 API 文档

## 概述

金刚位配置管理API提供了完整的金刚位配置功能，包括增删改查、排序、状态管理等操作。

**基础路径**: `/api/diamond-position`

## 数据模型

### OtoDiamondPosition 实体

| 字段名 | 类型 | 说明 | 必填 |
|--------|------|------|------|
| id | Long | 主键ID | 否 |
| name | String | 金刚位名称 | 是 |
| icon | String | 图标内容 | 否 |
| iconType | String | 图标类型(url/svg/base64/emoji) | 否 |
| url | String | 跳转链接 | 否 |
| sortOrder | Integer | 排序序号 | 否 |
| status | Integer | 状态(0-禁用,1-启用) | 否 |
| description | String | 描述 | 否 |
| createdTime | LocalDateTime | 创建时间 | 否 |
| updatedTime | LocalDateTime | 更新时间 | 否 |
| createdBy | String | 创建人 | 否 |
| updatedBy | String | 更新人 | 否 |

## API 接口

### 1. 分页查询金刚位列表

**接口地址**: `GET /api/diamond-position/page`

**权限要求**: `front:diamond:list`

**请求参数**:
```json
{
  "name": "金刚位名称(模糊查询)",
  "iconType": "图标类型",
  "status": "状态",
  "pageNum": 1,
  "pageSize": 10
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "total": 12,
    "rows": [
      {
        "id": 1,
        "name": "扫一扫",
        "icon": "https://example.com/icons/scan.png",
        "iconType": "url",
        "url": "/scan",
        "sortOrder": 1,
        "status": 1,
        "description": "扫码功能",
        "createdTime": "2025-01-23T10:00:00"
      }
    ]
  }
}
```

### 2. 获取所有启用的金刚位

**接口地址**: `GET /api/diamond-position/list`

**权限要求**: 无（公开接口）

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "扫一扫",
      "icon": "https://example.com/icons/scan.png",
      "iconType": "url",
      "url": "/scan",
      "sortOrder": 1,
      "status": 1
    }
  ]
}
```

### 3. 获取金刚位详情

**接口地址**: `GET /api/diamond-position/{id}`

**权限要求**: `front:diamond:query`

**路径参数**:
- `id`: 金刚位ID

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "name": "扫一扫",
    "icon": "https://example.com/icons/scan.png",
    "iconType": "url",
    "url": "/scan",
    "sortOrder": 1,
    "status": 1,
    "description": "扫码功能"
  }
}
```

### 4. 新增金刚位

**接口地址**: `POST /api/diamond-position`

**权限要求**: `front:diamond:add`

**请求体**:
```json
{
  "name": "新金刚位",
  "icon": "https://example.com/icon.png",
  "iconType": "url",
  "url": "/new-feature",
  "sortOrder": 10,
  "status": 1,
  "description": "新功能描述"
}
```

### 5. 更新金刚位

**接口地址**: `PUT /api/diamond-position/{id}`

**权限要求**: `front:diamond:edit`

**路径参数**:
- `id`: 金刚位ID

**请求体**:
```json
{
  "name": "更新后的名称",
  "icon": "https://example.com/new-icon.png",
  "iconType": "url",
  "url": "/updated-feature",
  "status": 1,
  "description": "更新后的描述"
}
```

### 6. 删除金刚位

**接口地址**: `DELETE /api/diamond-position/{id}`

**权限要求**: `front:diamond:remove`

**路径参数**:
- `id`: 金刚位ID

### 7. 批量删除金刚位

**接口地址**: `DELETE /api/diamond-position/batch`

**权限要求**: `front:diamond:remove`

**请求体**:
```json
[1, 2, 3, 4]
```

### 8. 更新金刚位排序

**接口地址**: `PUT /api/diamond-position/sort`

**权限要求**: `front:diamond:edit`

**请求体**:
```json
[
  {"id": 1, "sortOrder": 1},
  {"id": 2, "sortOrder": 2},
  {"id": 3, "sortOrder": 3}
]
```

### 9. 更新金刚位状态

**接口地址**: `PUT /api/diamond-position/{id}/status`

**权限要求**: `front:diamond:edit`

**路径参数**:
- `id`: 金刚位ID

**查询参数**:
- `status`: 状态值(0-禁用,1-启用)

### 10. 批量更新状态

**接口地址**: `PATCH /api/diamond-position/batch/status`

**权限要求**: `front:diamond:edit`

**请求体**:
```json
{
  "ids": [1, 2, 3],
  "status": 1
}
```

### 11. 导出金刚位配置

**接口地址**: `POST /api/diamond-position/export`

**权限要求**: `front:diamond:export`

**请求体**: 查询条件（同分页查询）

**响应**: Excel文件下载

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 图标类型说明

| 类型 | 说明 | 示例 |
|------|------|------|
| url | 图片链接地址 | `https://example.com/icon.png` |
| svg | SVG矢量图代码 | `<svg>...</svg>` |
| base64 | Base64编码图片 | `data:image/png;base64,iVBORw0...` |
| emoji | Unicode表情符号 | `💰` `🔧` `📅` |
