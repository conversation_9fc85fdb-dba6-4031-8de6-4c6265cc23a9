# 金刚位配置管理前端开发指南

## 概述

本文档为前端团队提供金刚位配置管理功能的开发指导，基于已有的Vue组件设计进行补充说明。

## 后端API集成

### API服务封装

建议在 `src/api/` 目录下创建 `diamondPosition.js`：

```javascript
import request from '@/utils/request'

// 分页查询金刚位列表
export function getDiamondPositionPage(params) {
  return request({
    url: '/api/diamond-position/page',
    method: 'get',
    params
  })
}

// 获取所有启用的金刚位
export function getDiamondPositionList() {
  return request({
    url: '/api/diamond-position/list',
    method: 'get'
  })
}

// 获取金刚位详情
export function getDiamondPosition(id) {
  return request({
    url: `/api/diamond-position/${id}`,
    method: 'get'
  })
}

// 新增金刚位
export function addDiamondPosition(data) {
  return request({
    url: '/api/diamond-position',
    method: 'post',
    data
  })
}

// 更新金刚位
export function updateDiamondPosition(id, data) {
  return request({
    url: `/api/diamond-position/${id}`,
    method: 'put',
    data
  })
}

// 删除金刚位
export function deleteDiamondPosition(id) {
  return request({
    url: `/api/diamond-position/${id}`,
    method: 'delete'
  })
}

// 批量删除
export function batchDeleteDiamondPosition(ids) {
  return request({
    url: '/api/diamond-position/batch',
    method: 'delete',
    data: ids
  })
}

// 更新排序
export function updateDiamondPositionSort(data) {
  return request({
    url: '/api/diamond-position/sort',
    method: 'put',
    data
  })
}

// 更新状态
export function updateDiamondPositionStatus(id, status) {
  return request({
    url: `/api/diamond-position/${id}/status`,
    method: 'put',
    params: { status }
  })
}

// 批量更新状态
export function batchUpdateDiamondPositionStatus(ids, status) {
  return request({
    url: '/api/diamond-position/batch/status',
    method: 'patch',
    data: { ids, status }
  })
}
```

### 数据类型定义

建议在 `src/types/` 目录下创建 `diamondPosition.ts`：

```typescript
export interface DiamondPosition {
  id?: number
  name: string
  icon?: string
  iconType?: 'url' | 'svg' | 'base64' | 'emoji'
  url?: string
  sortOrder?: number
  status?: 0 | 1
  description?: string
  createdTime?: string
  updatedTime?: string
  createdBy?: string
  updatedBy?: string
}

export interface DiamondPositionQuery {
  name?: string
  iconType?: string
  status?: number
  pageNum?: number
  pageSize?: number
}

export interface SortItem {
  id: number
  sortOrder: number
}
```

## 组件集成要点

### 1. 现有组件结构

基于设计文档中的Vue组件，主要包含：
- `DiamondPositionManage.vue` - 主管理页面
- `DiamondPositionDialog.vue` - 编辑对话框
- `composables/` - 组合式函数
- `types/` - 类型定义

### 2. 关键功能实现

#### 图标类型处理
```javascript
// 图标渲染逻辑
const renderIcon = (icon, iconType) => {
  switch (iconType) {
    case 'url':
      return `<img src="${icon}" alt="icon" style="width: 24px; height: 24px;" />`
    case 'svg':
      return icon
    case 'base64':
      return `<img src="${icon}" alt="icon" style="width: 24px; height: 24px;" />`
    case 'emoji':
      return `<span style="font-size: 24px;">${icon}</span>`
    default:
      return icon
  }
}
```

#### 拖拽排序集成
```javascript
// 使用 Sortable.js 实现拖拽排序
import Sortable from 'sortablejs'

const initSortable = () => {
  const el = document.querySelector('.diamond-grid')
  Sortable.create(el, {
    animation: 150,
    onEnd: async (evt) => {
      const { oldIndex, newIndex } = evt
      // 更新本地数据
      const item = diamondList.value.splice(oldIndex, 1)[0]
      diamondList.value.splice(newIndex, 0, item)
      
      // 重新计算排序
      const sortData = diamondList.value.map((item, index) => ({
        id: item.id,
        sortOrder: index + 1
      }))
      
      // 调用API更新排序
      await updateDiamondPositionSort(sortData)
    }
  })
}
```

### 3. 状态管理建议

使用 Pinia 进行状态管理：

```javascript
// stores/diamondPosition.js
import { defineStore } from 'pinia'
import { getDiamondPositionList } from '@/api/diamondPosition'

export const useDiamondPositionStore = defineStore('diamondPosition', {
  state: () => ({
    diamondList: [],
    loading: false
  }),
  
  actions: {
    async fetchDiamondList() {
      this.loading = true
      try {
        const { data } = await getDiamondPositionList()
        this.diamondList = data
      } finally {
        this.loading = false
      }
    }
  }
})
```

## 权限控制

### 按钮权限控制
```vue
<template>
  <el-button 
    v-if="hasPermission('front:diamond:add')"
    type="primary" 
    @click="handleAdd"
  >
    新增金刚位
  </el-button>
</template>

<script setup>
import { hasPermission } from '@/utils/permission'
</script>
```

### 路由权限配置
```javascript
// router/modules/front.js
export default {
  path: '/front',
  component: Layout,
  children: [
    {
      path: 'diamond-position',
      component: () => import('@/views/front/config/diamond-position/index.vue'),
      name: 'DiamondPosition',
      meta: {
        title: '金刚位配置',
        icon: 'grid',
        permissions: ['front:diamond:list']
      }
    }
  ]
}
```

## 响应式设计

### 移动端适配
```scss
.diamond-grid {
  display: grid;
  gap: 16px;
  
  // 桌面端：4列
  @media (min-width: 1024px) {
    grid-template-columns: repeat(4, 1fr);
  }
  
  // 平板端：3列
  @media (min-width: 768px) and (max-width: 1023px) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  // 手机端：2列
  @media (max-width: 767px) {
    grid-template-columns: repeat(2, 1fr);
  }
}
```

## 错误处理

### 统一错误处理
```javascript
// utils/errorHandler.js
export const handleApiError = (error) => {
  const { response } = error
  if (response) {
    switch (response.status) {
      case 401:
        ElMessage.error('登录已过期，请重新登录')
        // 跳转到登录页
        break
      case 403:
        ElMessage.error('权限不足')
        break
      case 404:
        ElMessage.error('资源不存在')
        break
      default:
        ElMessage.error(response.data?.msg || '操作失败')
    }
  } else {
    ElMessage.error('网络错误，请检查网络连接')
  }
}
```

## 性能优化建议

1. **图片懒加载**: 对于URL类型的图标，使用懒加载
2. **虚拟滚动**: 当金刚位数量较多时，考虑使用虚拟滚动
3. **缓存策略**: 对启用的金刚位列表进行缓存
4. **防抖处理**: 搜索功能使用防抖，避免频繁请求

## 测试建议

### 单元测试
```javascript
// tests/unit/DiamondPosition.spec.js
import { mount } from '@vue/test-utils'
import DiamondPositionManage from '@/views/front/config/diamond-position/index.vue'

describe('DiamondPositionManage', () => {
  it('should render correctly', () => {
    const wrapper = mount(DiamondPositionManage)
    expect(wrapper.find('.diamond-position-manage').exists()).toBe(true)
  })
})
```

### E2E测试场景
1. 新增金刚位流程
2. 编辑金刚位信息
3. 拖拽排序功能
4. 批量操作功能
5. 状态切换功能

## 部署注意事项

1. **图标资源**: 确保图标资源的CDN配置正确
2. **权限同步**: 确保前端权限配置与后端一致
3. **缓存策略**: 配置合适的静态资源缓存策略
