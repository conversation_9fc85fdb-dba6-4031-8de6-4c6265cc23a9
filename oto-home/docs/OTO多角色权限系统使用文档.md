# OTO多角色权限系统使用文档

## 📖 概述

OTO多角色权限系统是基于Spring Boot和MyBatis-Plus开发的企业级权限管理解决方案，支持多角色、权限继承、灵活的权限验证策略。

## 🏗️ 系统架构

### 核心组件

```
OTO权限系统
├── 权限服务层 (OtoMemberPermissionServiceImpl)
├── 权限常量 (MemberPermissionConstants)
├── 权限注解 (@RequireServiceProvider, @RequireMultiRole)
├── 权限切面 (MemberPermissionAspect)
└── 登录集成 (OtoMemberLoginServiceImpl, SysLoginService)
```

### 权限模型

```
普通用户 (Consumer)
├── 基础权限: member:profile:view, member:course:view
└── 服务提供者 (Service Provider)
    ├── 瑜伽老师 (Yoga Teacher)
    │   ├── 继承: 普通用户权限
    │   └── 专属: member:yoga:*, member:schedule:*
    ├── 健身教练 (Fitness Coach)
    │   ├── 继承: 普通用户权限
    │   └── 专属: member:fitness:*, member:equipment:*
    └── 营养师 (Nutritionist)
        ├── 继承: 普通用户权限
        └── 专属: member:nutrition:*, member:diet:*
```

## 🚀 快速开始

### 1. 数据库初始化

执行SQL脚本初始化权限数据：

```sql
-- 执行 script/sql/member_permission_init.sql
source /path/to/member_permission_init.sql;
```

### 2. 权限注解使用

#### @RequireServiceProvider 注解

用于验证用户是否具有指定的服务提供者身份：

```java
@RestController
@RequestMapping("/api/yoga")
public class YogaController {
    
    // 只允许瑜伽老师访问
    @RequireServiceProvider(ServiceProviderType.YOGA_TEACHER)
    @GetMapping("/classes")
    public R<List<YogaClass>> getYogaClasses() {
        // 业务逻辑
    }
    
    // 允许瑜伽老师或健身教练访问
    @RequireServiceProvider(
        value = {ServiceProviderType.YOGA_TEACHER, ServiceProviderType.FITNESS_COACH},
        logical = Logical.OR
    )
    @GetMapping("/equipment")
    public R<List<Equipment>> getEquipment() {
        // 业务逻辑
    }
    
    // 允许普通用户访问
    @RequireServiceProvider(
        value = ServiceProviderType.YOGA_TEACHER,
        includeConsumer = true
    )
    @GetMapping("/schedule")
    public R<List<Schedule>> getSchedule() {
        // 业务逻辑
    }
}
```

#### @RequireMultiRole 注解

用于验证用户是否具有指定的多个角色：

```java
@RestController
@RequestMapping("/api/admin")
public class AdminController {
    
    // 需要同时具有管理员和瑜伽老师角色
    @RequireMultiRole(
        value = {"admin", "yoga_teacher"},
        logical = Logical.AND
    )
    @PostMapping("/yoga/approve")
    public R<Void> approveYogaClass(@RequestBody YogaClassBo bo) {
        // 业务逻辑
    }
    
    // 具有管理员或超级用户角色之一即可
    @RequireMultiRole(
        value = {"admin", "super_user"},
        logical = Logical.OR
    )
    @DeleteMapping("/users/{id}")
    public R<Void> deleteUser(@PathVariable Long id) {
        // 业务逻辑
    }
}
```

### 3. 权限服务使用

在业务代码中直接使用权限服务：

```java
@Service
@RequiredArgsConstructor
public class CourseServiceImpl implements ICourseService {
    
    private final IOtoMemberPermissionService permissionService;
    
    public R<List<Course>> getCourses() {
        // 检查用户是否为服务提供者
        if (permissionService.isServiceProvider()) {
            // 返回服务提供者可见的课程
            return getProviderCourses();
        } else {
            // 返回普通用户可见的课程
            return getConsumerCourses();
        }
    }
    
    public R<Void> createCourse(CourseBo bo) {
        // 检查用户是否具有特定服务提供者角色
        Set<String> providerTypes = permissionService.getServiceProviderTypes();
        if (providerTypes.contains("yoga_teacher") && bo.getType().equals("yoga")) {
            // 瑜伽老师可以创建瑜伽课程
            return createYogaCourse(bo);
        }
        throw new ServiceException("权限不足");
    }
}
```

## 🔧 配置说明

### 权限常量配置

在 `MemberPermissionConstants` 中定义权限常量：

```java
public class MemberPermissionConstants {
    
    // 普通用户默认角色
    public static final List<String> DEFAULT_CONSUMER_ROLES = Arrays.asList("consumer");
    
    // 普通用户默认权限
    public static final List<String> DEFAULT_CONSUMER_PERMISSIONS = Arrays.asList(
        "member:profile:view",
        "member:course:view",
        "member:schedule:view"
    );
    
    // 服务提供者角色定义
    public static final String YOGA_TEACHER_ROLE = "yoga_teacher";
    public static final String FITNESS_COACH_ROLE = "fitness_coach";
    public static final String NUTRITIONIST_ROLE = "nutritionist";
    
    // 服务提供者权限定义
    public static final List<String> YOGA_TEACHER_PERMISSIONS = Arrays.asList(
        "member:yoga:create",
        "member:yoga:edit",
        "member:yoga:delete",
        "member:schedule:manage"
    );
}
```

### 切面配置

权限切面自动生效，无需额外配置。切面会拦截带有权限注解的方法并进行权限验证。

## 📚 API参考

### IOtoMemberPermissionService 接口

```java
public interface IOtoMemberPermissionService {
    
    /**
     * 获取用户角色权限
     * @param userId 用户ID
     * @return 权限列表
     */
    Set<String> getRolePermission(Long userId);
    
    /**
     * 获取用户菜单权限
     * @param userId 用户ID
     * @return 菜单权限列表
     */
    Set<String> getMenuPermission(Long userId);
    
    /**
     * 判断是否为普通用户
     * @param userId 用户ID
     * @return 是否为普通用户
     */
    boolean isConsumer(Long userId);
    
    /**
     * 判断是否为服务提供者
     * @param userId 用户ID
     * @return 是否为服务提供者
     */
    boolean isServiceProvider(Long userId);
    
    /**
     * 获取服务提供者类型
     * @param userId 用户ID
     * @return 服务提供者类型集合
     */
    Set<String> getServiceProviderTypes(Long userId);
    
    /**
     * 检查是否具有指定服务提供者角色
     * @param userId 用户ID
     * @param serviceProviderRole 服务提供者角色
     * @return 是否具有指定角色
     */
    boolean hasServiceProviderRole(Long userId, String serviceProviderRole);
    
    /**
     * 获取当前登录用户权限（无参数版本）
     */
    Set<String> getRolePermission();
    Set<String> getMenuPermission();
    boolean isConsumer();
    boolean isServiceProvider();
    Set<String> getServiceProviderTypes();
    boolean hasServiceProviderRole(String serviceProviderRole);
}
```

### 权限注解参数

#### @RequireServiceProvider

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | ServiceProviderType[] | {} | 所需的服务提供者类型 |
| logical | Logical | AND | 逻辑关系（AND/OR） |
| includeConsumer | boolean | false | 是否允许普通用户访问 |
| message | String | "权限不足" | 自定义错误消息 |

#### @RequireMultiRole

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | String[] | {} | 所需的角色列表 |
| logical | Logical | AND | 逻辑关系（AND/OR） |
| includeConsumer | boolean | false | 是否包含普通用户角色验证 |
| message | String | "权限不足" | 自定义错误消息 |

## 🔍 故障排除

### 常见问题

#### 1. 权限验证失败

**问题**: 用户明明有权限，但是验证失败

**解决方案**:
- 检查用户角色是否正确分配
- 确认权限常量定义是否正确
- 验证数据库中的权限数据是否完整

#### 2. 注解不生效

**问题**: 权限注解没有起作用

**解决方案**:
- 确认切面类 `MemberPermissionAspect` 已被Spring扫描
- 检查方法是否为public
- 确认类是否被Spring管理（有@Component等注解）

#### 3. 登录后权限为空

**问题**: 用户登录后获取不到权限信息

**解决方案**:
- 检查 `OtoMemberLoginServiceImpl.loadUserPermissions` 方法
- 确认权限服务注入是否正确
- 验证用户ID是否正确传递

### 调试技巧

#### 1. 开启权限调试日志

在 `application.yml` 中添加：

```yaml
logging:
  level:
    com.oto.member.service.impl.OtoMemberPermissionServiceImpl: DEBUG
    com.oto.member.aspect.MemberPermissionAspect: DEBUG
```

#### 2. 权限验证测试

```java
@Test
public void testPermission() {
    Long userId = 1L;
    
    // 测试基础权限
    Set<String> permissions = permissionService.getRolePermission(userId);
    System.out.println("用户权限: " + permissions);
    
    // 测试服务提供者身份
    boolean isProvider = permissionService.isServiceProvider(userId);
    System.out.println("是否为服务提供者: " + isProvider);
    
    // 测试具体角色
    Set<String> providerTypes = permissionService.getServiceProviderTypes(userId);
    System.out.println("服务提供者类型: " + providerTypes);
}
```

## 📈 性能优化

### 缓存策略

权限信息会被缓存以提高性能：

```java
@Cacheable(cacheNames = "member_permissions", key = "#userId")
public Set<String> getRolePermission(Long userId) {
    // 权限查询逻辑
}
```

### 批量权限检查

对于需要批量检查权限的场景，建议使用批量接口：

```java
public Map<Long, Set<String>> batchGetRolePermissions(List<Long> userIds) {
    // 批量查询实现
}
```

## 🔒 安全考虑

### 权限提升防护

- 权限验证在切面层进行，无法绕过
- 所有权限检查都基于数据库中的实际数据
- 支持动态权限更新，无需重启应用

### 敏感操作保护

对于敏感操作，建议使用多重验证：

```java
@RequireServiceProvider(ServiceProviderType.ADMIN)
@RequireMultiRole(value = {"admin", "super_user"}, logical = Logical.AND)
@PostMapping("/sensitive-operation")
public R<Void> sensitiveOperation() {
    // 敏感操作逻辑
}
```

## 📝 更新日志

### v1.0.0 (2025-08-15)

- ✅ 实现多角色权限系统
- ✅ 支持权限继承机制
- ✅ 提供声明式权限验证注解
- ✅ 集成登录权限加载
- ✅ 完善权限验证切面
- ✅ 修复登录权限加载报错问题

## 🤝 贡献指南

### 扩展新的服务提供者类型

1. 在 `MemberPermissionConstants` 中添加新的角色和权限常量
2. 在 `ServiceProviderType` 枚举中添加新类型
3. 更新数据库初始化脚本
4. 编写相应的单元测试

### 添加新的权限验证注解

1. 创建新的注解类
2. 在 `MemberPermissionAspect` 中添加对应的切点和处理逻辑
3. 编写使用示例和测试用例

---

**📞 技术支持**: 如有问题，请联系开发团队或查看项目文档。

**🔗 相关链接**:
- [项目源码](https://github.com/your-org/oto-home)
- [API文档](http://localhost:8081/doc.html)
- [问题反馈](https://github.com/your-org/oto-home/issues)