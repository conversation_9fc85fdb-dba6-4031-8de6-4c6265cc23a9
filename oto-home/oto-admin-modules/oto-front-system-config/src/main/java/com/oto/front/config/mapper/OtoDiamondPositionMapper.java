package com.oto.front.config.mapper;

import com.oto.common.mybatis.core.mapper.BaseMapperPlus;
import com.oto.front.config.domain.OtoDiamondPosition;
import com.oto.front.config.domain.vo.OtoDiamondPositionVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 金刚位配置Mapper接口
 *
 * <AUTHOR>
 */
public interface OtoDiamondPositionMapper extends BaseMapperPlus<OtoDiamondPosition, OtoDiamondPositionVo> {

    /**
     * 查询所有启用的金刚位（按排序顺序）
     *
     * @return 启用的金刚位列表
     */
    List<OtoDiamondPositionVo> selectEnabledList();

    /**
     * 获取最大排序序号
     *
     * @return 最大排序序号
     */
    Integer selectMaxSortOrder();

    /**
     * 批量更新状态
     *
     * @param ids 金刚位ID列表
     * @param status 状态值
     * @return 更新行数
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") Integer status);

    /**
     * 批量更新排序
     *
     * @param items 排序项列表
     * @return 更新行数
     */
    int batchUpdateSortOrder(@Param("items") List<OtoDiamondPosition> items);

}
