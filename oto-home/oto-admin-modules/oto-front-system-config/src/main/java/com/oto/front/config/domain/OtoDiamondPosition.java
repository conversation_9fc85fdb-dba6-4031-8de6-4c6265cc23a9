package com.oto.front.config.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.oto.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 金刚位配置表 oto_diamond_position
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("oto_diamond_position")
public class OtoDiamondPosition extends TenantEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 金刚位名称
     */
    @TableField("name")
    private String name;

    /**
     * 图标内容（URL、SVG代码、Base64等）
     */
    @TableField("icon")
    private String icon;

    /**
     * 图标类型：url-链接地址，svg-SVG代码，base64-Base64编码，emoji-表情符号
     */
    @TableField("icon_type")
    private String iconType;

    /**
     * 跳转链接
     */
    @TableField("url")
    private String url;

    /**
     * 排序序号
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 状态：0-禁用，1-启用
     */
    @TableField("status")
    private Integer status;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    // 注意：创建时间、更新时间、创建人、更新人字段已在BaseEntity中定义
    // 这里不需要重复定义，BaseEntity中的字段会自动映射到对应的数据库字段

}
