package com.oto.front.config.domain.vo;


import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.oto.common.excel.annotation.ExcelDictFormat;
import com.oto.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 金刚位配置视图对象 oto_diamond_position
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = com.oto.front.config.domain.OtoDiamondPosition.class)
public class OtoDiamondPositionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 金刚位名称
     */
    @ExcelProperty(value = "金刚位名称")
    private String name;

    /**
     * 图标内容（URL、SVG代码、Base64等）
     */
    @ExcelProperty(value = "图标内容")
    private String icon;

    /**
     * 图标类型：url-链接地址，svg-SVG代码，base64-Base64编码，emoji-表情符号
     */
    @ExcelProperty(value = "图标类型")
    private String iconType;

    /**
     * 跳转链接
     */
    @ExcelProperty(value = "跳转链接")
    private String url;

    /**
     * 排序序号
     */
    @ExcelProperty(value = "排序序号")
    private Integer sortOrder;

    /**
     * 状态：0-禁用，1-启用
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_normal_disable")
    private Integer status;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String description;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private Long createBy;

    /**
     * 更新人
     */
    @ExcelProperty(value = "更新人")
    private Long updateBy;

}
