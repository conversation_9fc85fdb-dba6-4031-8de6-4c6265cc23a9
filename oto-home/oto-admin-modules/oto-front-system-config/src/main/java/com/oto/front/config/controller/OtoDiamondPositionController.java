package com.oto.front.config.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.oto.common.core.domain.R;
import com.oto.common.core.validate.AddGroup;
import com.oto.common.core.validate.EditGroup;
import com.oto.common.excel.utils.ExcelUtil;
import com.oto.common.log.annotation.Log;
import com.oto.common.log.enums.BusinessType;
import com.oto.common.mybatis.core.page.PageQuery;
import com.oto.common.mybatis.core.page.TableDataInfo;
import com.oto.common.web.core.BaseController;
import com.oto.front.config.domain.bo.OtoDiamondPositionBo;
import com.oto.front.config.domain.vo.OtoDiamondPositionVo;
import com.oto.front.config.service.IOtoDiamondPositionService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 金刚位配置管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/diamond-position")
public class OtoDiamondPositionController extends BaseController {

    private final IOtoDiamondPositionService otoDiamondPositionService;

    /**
     * 分页查询金刚位列表
     */
    @SaCheckPermission("front:diamond:list")
    @GetMapping("/page")
    public R<TableDataInfo<OtoDiamondPositionVo>> page(OtoDiamondPositionBo bo, PageQuery pageQuery) {
        return R.ok(otoDiamondPositionService.queryPageList(bo, pageQuery));
    }

    /**
     * 获取所有启用的金刚位（用于前端展示）
     */
    @GetMapping("/list")
    public R<List<OtoDiamondPositionVo>> list() {
        return R.ok(otoDiamondPositionService.queryEnabledList());
    }

    /**
     * 导出金刚位配置列表
     */
    @SaCheckPermission("front:diamond:export")
    @Log(title = "金刚位配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OtoDiamondPositionBo bo, HttpServletResponse response) {
        List<OtoDiamondPositionVo> list = otoDiamondPositionService.queryList(bo);
        ExcelUtil.exportExcel(list, "金刚位配置", OtoDiamondPositionVo.class, response);
    }

    /**
     * 根据ID获取金刚位详情
     */
    @SaCheckPermission("front:diamond:query")
    @GetMapping("/{id}")
    public R<OtoDiamondPositionVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(otoDiamondPositionService.queryById(id));
    }

    /**
     * 新增金刚位
     */
    @SaCheckPermission("front:diamond:add")
    @Log(title = "金刚位配置", businessType = BusinessType.INSERT)

    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OtoDiamondPositionBo bo) {
        return toAjax(otoDiamondPositionService.insertByBo(bo));
    }

    /**
     * 更新金刚位
     */
    @SaCheckPermission("front:diamond:edit")
    @Log(title = "金刚位配置", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}")
    public R<Void> edit(@PathVariable Long id,
                        @Validated(EditGroup.class) @RequestBody OtoDiamondPositionBo bo) {
        bo.setId(id);
        return toAjax(otoDiamondPositionService.updateByBo(bo));
    }

    /**
     * 删除金刚位
     */
    @SaCheckPermission("front:diamond:remove")
    @Log(title = "金刚位配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Void> remove(@NotNull(message = "主键不能为空")
                          @PathVariable Long id) {
        return toAjax(otoDiamondPositionService.deleteWithValidByIds(List.of(id), true));
    }

    /**
     * 批量删除金刚位
     */
    @SaCheckPermission("front:diamond:remove")
    @Log(title = "金刚位配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch")
    public R<Void> batchRemove(@RequestBody List<Long> ids) {
        return toAjax(otoDiamondPositionService.deleteWithValidByIds(ids, true));
    }

    /**
     * 更新金刚位排序
     */
    @SaCheckPermission("front:diamond:edit")
    @Log(title = "金刚位配置", businessType = BusinessType.UPDATE)
    @PutMapping("/sort")
    public R<Void> updateSort(@RequestBody List<OtoDiamondPositionBo> sortList) {
        return toAjax(otoDiamondPositionService.updateSortOrder(sortList));
    }

    /**
     * 启用/禁用金刚位
     */
    @SaCheckPermission("front:diamond:edit")
    @Log(title = "金刚位配置", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/status")
    public R<Void> updateStatus(@PathVariable Long id,
                                @RequestParam Integer status) {
        return toAjax(otoDiamondPositionService.batchUpdateStatus(List.of(id), status));
    }

    /**
     * 批量更新状态
     */
    @SaCheckPermission("front:diamond:edit")
    @Log(title = "金刚位配置", businessType = BusinessType.UPDATE)
    @PatchMapping("/batch/status")
    public R<Void> batchUpdateStatus(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<Long> ids = (List<Long>) request.get("ids");
        Integer status = (Integer) request.get("status");
        return toAjax(otoDiamondPositionService.batchUpdateStatus(ids, status));
    }

}
