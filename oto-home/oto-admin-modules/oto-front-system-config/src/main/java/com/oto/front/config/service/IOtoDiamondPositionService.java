package com.oto.front.config.service;

import com.oto.common.mybatis.core.page.PageQuery;
import com.oto.common.mybatis.core.page.TableDataInfo;
import com.oto.front.config.domain.bo.OtoDiamondPositionBo;
import com.oto.front.config.domain.vo.OtoDiamondPositionVo;

import java.util.Collection;
import java.util.List;

/**
 * 金刚位配置Service接口
 *
 * <AUTHOR>
 */
public interface IOtoDiamondPositionService {

    /**
     * 查询金刚位配置
     *
     * @param id 金刚位配置主键
     * @return 金刚位配置
     */
    OtoDiamondPositionVo queryById(Long id);

    /**
     * 查询金刚位配置列表
     *
     * @param bo 金刚位配置
     * @param pageQuery 分页查询
     * @return 金刚位配置集合
     */
    TableDataInfo<OtoDiamondPositionVo> queryPageList(OtoDiamondPositionBo bo, PageQuery pageQuery);

    /**
     * 查询金刚位配置列表
     *
     * @param bo 金刚位配置
     * @return 金刚位配置集合
     */
    List<OtoDiamondPositionVo> queryList(OtoDiamondPositionBo bo);

    /**
     * 查询所有启用的金刚位（按排序顺序）
     *
     * @return 启用的金刚位列表
     */
    List<OtoDiamondPositionVo> queryEnabledList();

    /**
     * 新增金刚位配置
     *
     * @param bo 金刚位配置
     * @return 结果
     */
    Boolean insertByBo(OtoDiamondPositionBo bo);

    /**
     * 修改金刚位配置
     *
     * @param bo 金刚位配置
     * @return 结果
     */
    Boolean updateByBo(OtoDiamondPositionBo bo);

    /**
     * 校验并批量删除金刚位配置信息
     *
     * @param ids 需要删除的金刚位配置主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量更新状态
     *
     * @param ids 金刚位ID列表
     * @param status 状态值
     * @return 结果
     */
    Boolean batchUpdateStatus(List<Long> ids, Integer status);

    /**
     * 更新排序
     *
     * @param items 排序项列表
     * @return 结果
     */
    Boolean updateSortOrder(List<OtoDiamondPositionBo> items);

}
