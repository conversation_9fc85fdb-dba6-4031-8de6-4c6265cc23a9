package com.oto.front.config.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.oto.common.core.utils.MapstructUtils;
import com.oto.common.core.utils.StringUtils;
import com.oto.common.mybatis.core.page.PageQuery;
import com.oto.common.mybatis.core.page.TableDataInfo;
import com.oto.front.config.domain.OtoDiamondPosition;
import com.oto.front.config.domain.bo.OtoDiamondPositionBo;
import com.oto.front.config.domain.vo.OtoDiamondPositionVo;
import com.oto.front.config.mapper.OtoDiamondPositionMapper;
import com.oto.front.config.service.IOtoDiamondPositionService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 金刚位配置Service业务层处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class OtoDiamondPositionServiceImpl implements IOtoDiamondPositionService {

    private final OtoDiamondPositionMapper baseMapper;

    /**
     * 查询金刚位配置
     */
    @Override
    public OtoDiamondPositionVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询金刚位配置列表
     */
    @Override
    public TableDataInfo<OtoDiamondPositionVo> queryPageList(OtoDiamondPositionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OtoDiamondPosition> lqw = buildQueryWrapper(bo);
        Page<OtoDiamondPositionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询金刚位配置列表
     */
    @Override
    public List<OtoDiamondPositionVo> queryList(OtoDiamondPositionBo bo) {
        LambdaQueryWrapper<OtoDiamondPosition> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询所有启用的金刚位（按排序顺序）
     */
    @Override
    public List<OtoDiamondPositionVo> queryEnabledList() {
        return baseMapper.selectEnabledList();
    }

    private LambdaQueryWrapper<OtoDiamondPosition> buildQueryWrapper(OtoDiamondPositionBo bo) {
        LambdaQueryWrapper<OtoDiamondPosition> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), OtoDiamondPosition::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getIconType()), OtoDiamondPosition::getIconType, bo.getIconType());
        lqw.eq(ObjectUtil.isNotNull(bo.getStatus()), OtoDiamondPosition::getStatus, bo.getStatus());
        lqw.orderByAsc(OtoDiamondPosition::getSortOrder);
        lqw.orderByAsc(OtoDiamondPosition::getId);
        return lqw;
    }

    /**
     * 新增金刚位配置
     */
    @Override
    public Boolean insertByBo(OtoDiamondPositionBo bo) {
        OtoDiamondPosition add = BeanUtil.toBean(bo, OtoDiamondPosition.class);
        validEntityBeforeSave(add);
        
        // 如果没有指定排序，设置为最大值+1
        if (add.getSortOrder() == null || add.getSortOrder() <= 0) {
            Integer maxSortOrder = baseMapper.selectMaxSortOrder();
            add.setSortOrder(maxSortOrder != null ? maxSortOrder + 1 : 1);
        }
        
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改金刚位配置
     */
    @Override
    public Boolean updateByBo(OtoDiamondPositionBo bo) {
        OtoDiamondPosition update = BeanUtil.toBean(bo, OtoDiamondPosition.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OtoDiamondPosition entity){
        // 可以在此处添加业务校验逻辑
    }

    /**
     * 批量删除金刚位配置
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            // 可以在此处添加删除前的校验逻辑
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 批量更新状态
     */
    @Override
    public Boolean batchUpdateStatus(List<Long> ids, Integer status) {
        return baseMapper.batchUpdateStatus(ids, status) > 0;
    }

    /**
     * 更新排序
     */
    @Override
    public Boolean updateSortOrder(List<OtoDiamondPositionBo> items) {
        List<OtoDiamondPosition> entities = MapstructUtils.convert(items, OtoDiamondPosition.class);
        return baseMapper.batchUpdateSortOrder(entities) > 0;
    }
}
