# OTO-Home 数据库设计与开发规范

> **适用范围**：MySQL数据库设计、MyBatis-Plus配置、数据访问层开发
> **数据库版本**：MySQL 8.0+

---

## 🗄️ 数据库设计规范

### 命名规范

#### 表命名
- **格式**：`{模块前缀}_{业务名称}`
- **示例**：`sys_user`（系统用户）、`biz_order`（业务订单）
- **规则**：小写字母+下划线，避免使用保留字

#### 字段命名
- **格式**：小写字母+下划线分隔
- **示例**：`user_id`、`create_time`、`update_by`
- **布尔字段**：使用 `is_` 前缀，如 `is_deleted`、`is_enabled`

#### 索引命名
- **主键索引**：`pk_{表名}`
- **唯一索引**：`uk_{表名}_{字段名}`
- **普通索引**：`idx_{表名}_{字段名}`
- **外键索引**：`fk_{表名}_{字段名}`

### 字段设计规范

#### 基础字段（必须）
```sql
CREATE TABLE `sys_example` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者', 
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '删除标志（0正常 1删除）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='示例表';
```

#### 数据类型选择
- **整数类型**：`bigint`（主键）、`int`（一般整数）、`tinyint`（状态标志）
- **字符串类型**：`varchar(n)`（变长）、`char(n)`（定长）、`text`（长文本）
- **时间类型**：`datetime`（日期时间）、`date`（日期）、`timestamp`（时间戳）
- **金额类型**：`decimal(10,2)`（避免精度丢失）

---

## 🔧 MyBatis-Plus配置规范

### 全局配置
```yaml
mybatis-plus:
  configuration:
    # 驼峰转下划线
    map-underscore-to-camel-case: true
    # 日志输出
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      # 主键策略
      id-type: ASSIGN_ID
      # 逻辑删除字段
      logic-delete-field: isDeleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      # 字段填充策略
      insert-strategy: NOT_NULL
      update-strategy: NOT_NULL
```

### 实体类配置
```java
@TableName("sys_user")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysUser extends BaseEntity {
    
    @TableId(type = IdType.ASSIGN_ID)
    private Long userId;
    
    @TableField("user_name")
    private String userName;
    
    @TableLogic
    private Integer isDeleted;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
```

---

## 🔍 查询优化规范

### 索引设计原则
1. **主键索引**：每个表必须有主键
2. **唯一索引**：业务唯一字段添加唯一索引
3. **复合索引**：多字段查询创建复合索引
4. **覆盖索引**：查询字段尽量被索引覆盖

### SQL编写规范
```java
// 推荐：使用QueryWrapper
QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
queryWrapper.eq("user_name", userName)
           .eq("status", 1)
           .orderByDesc("create_time");
List<SysUser> users = userMapper.selectList(queryWrapper);

// 复杂查询：使用XML映射
@Select("SELECT * FROM sys_user WHERE user_name = #{userName} AND status = 1")
List<SysUser> selectByUserName(@Param("userName") String userName);
```

### 分页查询
```java
// 使用MyBatis-Plus分页插件
Page<SysUser> page = new Page<>(pageNum, pageSize);
QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
queryWrapper.like("user_name", keyword);
IPage<SysUser> result = userMapper.selectPage(page, queryWrapper);
```

---

## 🔄 数据迁移规范

### 版本控制
- 使用Flyway进行数据库版本控制
- 迁移文件命名：`V{版本号}__{描述}.sql`
- 示例：`V1.0.1__create_user_table.sql`

### 迁移脚本规范
```sql
-- V1.0.1__create_user_table.sql
-- 创建用户表
CREATE TABLE `sys_user` (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `user_name` varchar(30) NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) NOT NULL COMMENT '用户昵称',
  `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
  `phone` varchar(11) DEFAULT '' COMMENT '手机号码',
  `sex` char(1) DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) DEFAULT '' COMMENT '密码',
  `status` char(1) DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `login_ip` varchar(128) DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `uk_user_name` (`user_name`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户信息表';
```

---

## 🔐 数据安全规范

### 敏感数据处理
- **密码加密**：使用BCrypt进行密码哈希
- **数据脱敏**：敏感字段在日志中脱敏显示
- **权限控制**：数据库用户权限最小化原则

### 备份策略
- **定期备份**：每日全量备份，每小时增量备份
- **备份验证**：定期验证备份文件完整性
- **恢复测试**：定期进行数据恢复演练

---

## 📊 性能监控

### 慢查询监控
```sql
-- 开启慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;
SET GLOBAL slow_query_log_file = '/var/log/mysql/slow.log';
```

### 性能分析
- 使用 `EXPLAIN` 分析查询执行计划
- 监控数据库连接数和活跃连接
- 定期分析表空间使用情况

---

## 🛠️ 开发工具

### 代码生成
- 使用MyBatis-Plus代码生成器
- 自定义模板适配项目规范
- 生成实体类、Mapper、Service等

### 数据库管理
- 推荐使用Navicat、DataGrip等工具
- 统一数据库连接配置
- 规范SQL脚本管理

---

*最后更新：2024年12月 | 维护团队：OTO项目组数据库团队*