# OTO-Home项目规则导航 - 后端服务项目完整导航

## 📋 项目导航说明

本文档是**OTO-Home后端服务项目**的完整规则导航，既可作为二级导航使用（从一级导航访问），也可作为独立的一级导航直接访问，确保完整的导航体验。

## 🏗️ 项目架构概览

### 项目定位
**OTO-Home** 是OTO项目组的核心后端服务项目，基于RuoYi-Vue-Plus框架开发，采用双入口架构设计：

- **oto-admin入口** (端口8080) - 后台管理系统
- **oto-front入口** (端口8081) - 会员业务系统

### 与其他项目的关联关系
```
oto-ui ────HTTP API───► oto-home (本项目)
    ▲                      ▲
    │                      │
    └─── 共享认证服务 ──────┘
    
fit-manage-ui ──HTTP API──► oto-home (本项目)
```

## 🧭 核心规则文档导航

### 📄 主要规则文档
- 📋 [项目开发规范](./project_rules.md) - **核心规则文档**，包含完整的开发规范和架构约束

### 📚 详细技术规范
- **[API开发规范](backend/api-standards.md)** - Controller、Service、数据访问层完整规范
- **[数据库设计规范](backend/database-standards.md)** - MySQL + MyBatis-Plus 设计与优化规范
- **[安全开发规范](backend/security-standards.md)** - 认证授权、数据安全、审计规范

### 🎯 规则文档核心内容概览

#### 1. 项目架构规范
- ✅ 双入口架构设计原则
- ✅ 模块依赖约束规则
- ✅ 目录结构规范
- ✅ 分层架构规范

#### 2. 代码开发规范
- ✅ Controller/Service/Mapper/Domain分层规范
- ✅ 命名规范和代码风格
- ✅ 注释和文档规范
- ✅ 工具类使用规范

#### 3. 数据安全规范
- ✅ 敏感数据加密存储
- ✅ 数据脱敏处理
- ✅ 隐私合规要求
- ✅ Bean Validation验证

#### 4. 技术组件规范
- ✅ 数据对象使用规范(Entity/BO/VO/DTO)
- ✅ 异常处理规范
- ✅ 公共组件使用规范
- ✅ AI助手交互规范

## 🔧 技术栈与依赖

### 核心技术栈
- **框架**: RuoYi-Vue-Plus
- **数据库**: MySQL + MyBatis-Plus
- **缓存**: Redis
- **安全**: Sa-Token
- **构建**: Maven

### 模块依赖结构
```
oto-admin (8080) ──► oto-admin-modules ──► oto-common
oto-front (8081) ──► oto-front-modules ──► oto-common
                                      ──► oto-extend
```

## 📚 项目内技术文档

### 业务设计文档
- 📄 [用户权限角色关系分析](../../用户权限角色关系分析.md) - 权限系统设计
- 📄 [敏感数据加密存储方案设计](../../敏感数据加密存储方案设计.md) - 数据安全方案
- 📄 [加解密与密钥轮换详细方案](../../加解密与密钥轮换详细方案.md) - 加密技术方案

### 功能开发指南
- 📄 [后端金刚位开发指南](../../docs/金刚位文档/后端金刚位开发指南.md) - 金刚位功能开发
- 📄 [金刚位预览功能技术实现方案](../../docs/金刚位文档/金刚位预览功能技术实现方案.md) - 预览功能实现

### 其他技术文档
- 📄 [明日事](../../明日事.md) - 开发计划和任务

## 🚨 重要开发约束

### 模块依赖约束（严格执行）
1. **禁止跨模块直接依赖** - admin-modules与front-modules完全隔离
2. **禁止反向依赖** - 业务模块不能依赖入口模块
3. **公共能力统一管理** - 通用功能必须放在oto-common中
4. **双入口独立部署** - 两个入口可独立启动和部署

### AI助手交互要求
⚠️ **重要**: 每次任务执行过程中，AI助手必须使用interactive-feedback工具与开发者进行交互，确保开发过程的可控性和准确性。

### 安全合规要求
- 🔒 敏感数据必须加密存储
- 🔍 所有操作必须留痕审计
- 🛡️ 外部接口必须鉴权
- 📝 隐私协议必须记录

## 🔗 导航链接

### 返回上级导航
- 🏠 [返回一级导航总入口](../../.trae/rules/unified_standards.md)

### 访问其他子项目
- 🎨 [oto-ui项目规则导航](../../oto-ui/.trae/rules/project_navigation.md)
- 💪 [fit-manage-ui项目规则导航](../../fit-manage-ui/.trae/rules/project_navigation.md)

### 通用规范参考
- 📖 [Java后端技术栈规范](../../docs/unified-standards/02-tech-stack/java-backend/)
- 📖 [通用开发规范](../../docs/unified-standards/01-general/)

---

*最后更新时间：2024年12月*  
*文档版本：v1.0*  
*维护者：OTO-Home项目组*

> 💡 **使用提示**: 本导航文件设计为既可独立使用，也可作为层级导航的一部分。无论通过哪种方式访问，都能获得完整的项目规则导航体验。