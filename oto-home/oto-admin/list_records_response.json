{"code": 200, "msg": "操作成功", "data": [{"id": 6, "name": "信用卡", "icon": "💳", "iconType": "emoji", "url": "/credit-card", "sortOrder": 2, "status": 1, "description": "信用卡服务", "createTime": "2025-08-23 14:16:50", "updateTime": "2025-08-23 21:56:47", "createBy": null, "updateBy": null}, {"id": 7, "name": "保洁服务", "icon": "https://example.com/icons/cleaning.png", "iconType": "url", "url": "/pages/service/cleaning", "sortOrder": 3, "status": 1, "description": "家庭保洁服务", "createTime": "2025-08-23 14:16:50", "updateTime": "2025-08-23 21:56:47", "createBy": null, "updateBy": null}, {"id": 10, "name": "测试新增", "icon": "🎯", "iconType": "emoji", "url": "/test-add", "sortOrder": 10, "status": 1, "description": "API测试新增的金刚位", "createTime": "2025-08-23 21:40:11", "updateTime": "2025-08-23 21:40:11", "createBy": 1, "updateBy": 1}]}