<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FitHome - tianai-captcha 滑块验证码演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fafafa;
        }
        
        .section h3 {
            color: #007bff;
            margin-top: 0;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        select, button {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
            border: none;
            padding: 12px 24px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            min-height: 20px;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .captcha-display {
            margin: 20px 0;
            text-align: center;
            border: 2px dashed #ddd;
            padding: 20px;
            border-radius: 8px;
        }
        
        .captcha-image {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .help-text {
            color: #6c757d;
            font-size: 12px;
            margin-top: 5px;
        }
        
        .type-description {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #e7f3ff;
            border-left: 4px solid #007bff;
            border-radius: 0 4px 4px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 FitHome - tianai-captcha 滑块验证码演示</h1>
        
        <!-- 验证码类型说明 -->
        <div class="section">
            <h3>📋 验证码类型说明</h3>
            <div class="type-description">
                <strong>SLIDER（滑块验证码）</strong>：用户需要拖动滑块到正确位置
            </div>
            <div class="type-description">
                <strong>ROTATE（旋转验证码）</strong>：用户需要旋转图片到正确角度
            </div>
            <div class="type-description">
                <strong>CONCAT（拼接验证码）</strong>：用户需要拼接图片到正确位置
            </div>
            <div class="type-description">
                <strong>WORD_IMAGE_CLICK（点选文字验证码）</strong>：用户需要按顺序点击指定文字
            </div>
        </div>
        
        <!-- 验证码生成测试 -->
        <div class="section">
            <h3>🎯 验证码生成测试</h3>
            <div class="form-group">
                <label for="captchaType">选择验证码类型：</label>
                <select id="captchaType">
                    <option value="SLIDER">SLIDER - 滑块验证码</option>
                    <option value="ROTATE">ROTATE - 旋转验证码</option>
                    <option value="CONCAT">CONCAT - 拼接验证码</option>
                    <option value="WORD_IMAGE_CLICK">WORD_IMAGE_CLICK - 点选文字验证码</option>
                </select>
                <div class="help-text">选择要测试的验证码类型</div>
            </div>
            
            <button onclick="generateCaptcha()">🔄 生成验证码</button>
            <button onclick="clearResults()">🗑️ 清空结果</button>
            
            <div id="generateResult" class="result"></div>
            
            <!-- 验证码显示区域 -->
            <div id="captchaDisplay" class="captcha-display" style="display: none;">
                <div id="captchaInfo"></div>
                <div id="captchaImageContainer"></div>
            </div>
        </div>
        
        <!-- 验证码校验测试 -->
        <div class="section">
            <h3>✅ 验证码校验测试</h3>
            <p class="help-text">先生成验证码，然后可以使用示例数据进行校验测试</p>
            
            <button onclick="verifyWithSampleData()" id="verifyBtn" disabled>🎯 使用示例数据校验</button>
            <button onclick="showVerifyJson()" id="showJsonBtn" disabled>📄 查看校验JSON</button>
            
            <div id="verifyResult" class="result"></div>
        </div>
        
        <!-- 系统资源信息 -->
        <div class="section">
            <h3>📊 系统资源信息</h3>
            <button onclick="getResourceInfo()">📈 获取资源统计</button>
            <div id="resourceResult" class="result"></div>
        </div>
        
        <!-- API 调用日志 -->
        <div class="section">
            <h3>📝 API 调用日志</h3>
            <button onclick="clearLogs()">🗑️ 清空日志</button>
            <div id="apiLogs" class="code-block">等待 API 调用...</div>
        </div>
        
        <!-- 使用说明 -->
        <div class="section">
            <h3>📖 使用说明</h3>
            <ol>
                <li><strong>生成验证码</strong>：选择验证码类型，点击"生成验证码"按钮</li>
                <li><strong>查看结果</strong>：生成成功后会显示验证码图片和相关信息</li>
                <li><strong>校验测试</strong>：可以使用示例数据测试校验功能</li>
                <li><strong>资源统计</strong>：查看系统加载的验证码资源信息</li>
                <li><strong>集成到业务</strong>：参考API调用日志，将验证码集成到登录/注册流程中</li>
            </ol>
            
            <h4>🔌 前端集成示例：</h4>
            <div class="code-block">
// 生成验证码
fetch('/slide-captcha/generate?type=SLIDER')
  .then(response => response.json())
  .then(data => {
    if (data.code === 200) {
      // 显示验证码图片
      console.log('验证码ID:', data.data.data.id);
      // 在用户完成验证后校验
    }
  });

// 校验验证码
const verifyData = {
  id: 'captcha-id',
  backgroundImageWidth: 350,
  backgroundImageHeight: 155,
  sliderImageWidth: 100,
  sliderImageHeight: 155,
  startSlidingTime: Date.now(),
  entSlidingTime: Date.now() + 2000,
  trackList: [{x: 0, y: 0, type: 'down', t: 100}, {x: 50, y: 0, type: 'move', t: 500}]
};

fetch('/slide-captcha/verify', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify(verifyData)
}).then(response => response.json());
            </div>
        </div>
    </div>

    <script>
        let currentCaptchaData = null;
        
        // 添加日志
        function addLog(message, type = 'info') {
            const logs = document.getElementById('apiLogs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logs.textContent += logEntry;
            logs.scrollTop = logs.scrollHeight;
        }
        
        // 清空日志
        function clearLogs() {
            document.getElementById('apiLogs').textContent = '日志已清空，等待 API 调用...\n';
        }
        
        // 清空结果
        function clearResults() {
            document.getElementById('generateResult').innerHTML = '';
            document.getElementById('verifyResult').innerHTML = '';
            document.getElementById('resourceResult').innerHTML = '';
            document.getElementById('captchaDisplay').style.display = 'none';
            document.getElementById('verifyBtn').disabled = true;
            document.getElementById('showJsonBtn').disabled = true;
            currentCaptchaData = null;
        }
        
        // 生成验证码
        async function generateCaptcha() {
            const type = document.getElementById('captchaType').value;
            const resultDiv = document.getElementById('generateResult');
            
            try {
                addLog(`发起生成验证码请求: type=${type}`);
                resultDiv.innerHTML = '<div class="info">正在生成验证码...</div>';
                
                const response = await fetch(`/slide-captcha/generate?type=${type}`);
                const data = await response.json();
                
                addLog(`生成验证码响应: ${JSON.stringify(data, null, 2)}`);
                
                if (data.code === 200 && data.data) {
                    currentCaptchaData = data.data;
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>✅ 验证码生成成功！</strong><br>
                            类型：${type}<br>
                            验证码ID：${data.data.data.id}<br>
                            生成时间：${new Date().toLocaleString()}
                        </div>
                    `;
                    
                    // 显示验证码图片
                    displayCaptcha(data.data);
                    
                    // 启用校验按钮
                    document.getElementById('verifyBtn').disabled = false;
                    document.getElementById('showJsonBtn').disabled = false;
                    
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <strong>❌ 验证码生成失败</strong><br>
                            错误信息：${data.msg || '未知错误'}
                        </div>
                    `;
                }
            } catch (error) {
                addLog(`生成验证码异常: ${error.message}`, 'error');
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>❌ 网络请求失败</strong><br>
                        错误信息：${error.message}
                    </div>
                `;
            }
        }
        
        // 显示验证码
        function displayCaptcha(captchaResponse) {
            const displayDiv = document.getElementById('captchaDisplay');
            const infoDiv = document.getElementById('captchaInfo');
            const imageContainer = document.getElementById('captchaImageContainer');
            
            const captchaData = captchaResponse.data;
            
            infoDiv.innerHTML = `
                <h4>验证码信息</h4>
                <p><strong>ID：</strong>${captchaData.id}</p>
                <p><strong>类型：</strong>${captchaData.captchaType}</p>
                <p><strong>背景图尺寸：</strong>${captchaData.backgroundImageWidth} × ${captchaData.backgroundImageHeight}</p>
                ${captchaData.sliderImageWidth ? `<p><strong>滑块图尺寸：</strong>${captchaData.sliderImageWidth} × ${captchaData.sliderImageHeight}</p>` : ''}
            `;
            
            let imageHtml = '';
            if (captchaData.backgroundImage) {
                imageHtml += `
                    <div style="margin-bottom: 15px;">
                        <h5>背景图片：</h5>
                        <img src="data:image/png;base64,${captchaData.backgroundImage}" 
                             class="captcha-image" alt="验证码背景图" />
                    </div>
                `;
            }
            
            if (captchaData.sliderImage) {
                imageHtml += `
                    <div style="margin-bottom: 15px;">
                        <h5>滑块图片：</h5>
                        <img src="data:image/png;base64,${captchaData.sliderImage}" 
                             class="captcha-image" alt="验证码滑块图" />
                    </div>
                `;
            }
            
            imageContainer.innerHTML = imageHtml;
            displayDiv.style.display = 'block';
        }
        
        // 使用示例数据校验
        async function verifyWithSampleData() {
            if (!currentCaptchaData) {
                alert('请先生成验证码！');
                return;
            }
            
            const resultDiv = document.getElementById('verifyResult');
            
            try {
                const captchaData = currentCaptchaData.data;
                
                // 构造示例校验数据
                const verifyData = {
                    id: captchaData.id,
                    captchaType: captchaData.captchaType,
                    backgroundImageWidth: captchaData.backgroundImageWidth,
                    backgroundImageHeight: captchaData.backgroundImageHeight,
                    sliderImageWidth: captchaData.sliderImageWidth || 0,
                    sliderImageHeight: captchaData.sliderImageHeight || 0,
                    startSlidingTime: Date.now() - 3000,
                    entSlidingTime: Date.now(),
                    trackList: [
                        {x: 0, y: 0, type: 'down', t: 100},
                        {x: 120, y: 0, type: 'move', t: 1500},
                        {x: 120, y: 0, type: 'up', t: 2000}
                    ]
                };
                
                addLog(`发起校验验证码请求: ${JSON.stringify(verifyData, null, 2)}`);
                resultDiv.innerHTML = '<div class="info">正在校验验证码...</div>';
                
                const response = await fetch('/slide-captcha/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(verifyData)
                });
                
                const data = await response.json();
                addLog(`校验验证码响应: ${JSON.stringify(data, null, 2)}`);
                
                if (data.code === 200) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>✅ 验证码校验成功！</strong><br>
                            校验结果：${data.data ? '通过' : '失败'}<br>
                            响应信息：${data.msg}<br>
                            校验时间：${new Date().toLocaleString()}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <strong>❌ 验证码校验失败</strong><br>
                            错误信息：${data.msg || '未知错误'}
                        </div>
                    `;
                }
            } catch (error) {
                addLog(`校验验证码异常: ${error.message}`, 'error');
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>❌ 校验请求失败</strong><br>
                        错误信息：${error.message}
                    </div>
                `;
            }
        }
        
        // 显示校验JSON数据
        function showVerifyJson() {
            if (!currentCaptchaData) {
                alert('请先生成验证码！');
                return;
            }
            
            const captchaData = currentCaptchaData.data;
            const verifyData = {
                id: captchaData.id,
                captchaType: captchaData.captchaType,
                backgroundImageWidth: captchaData.backgroundImageWidth,
                backgroundImageHeight: captchaData.backgroundImageHeight,
                sliderImageWidth: captchaData.sliderImageWidth || 0,
                sliderImageHeight: captchaData.sliderImageHeight || 0,
                startSlidingTime: Date.now() - 3000,
                entSlidingTime: Date.now(),
                trackList: [
                    {x: 0, y: 0, type: 'down', t: 100},
                    {x: 120, y: 0, type: 'move', t: 1500},
                    {x: 120, y: 0, type: 'up', t: 2000}
                ]
            };
            
            const jsonStr = JSON.stringify(verifyData, null, 2);
            
            const popup = window.open('', '_blank', 'width=600,height=400');
            popup.document.write(`
                <html>
                <head><title>校验数据 JSON</title></head>
                <body style="font-family: monospace; padding: 20px;">
                    <h3>校验验证码的 JSON 数据：</h3>
                    <pre style="background: #f5f5f5; padding: 15px; border-radius: 5px;">${jsonStr}</pre>
                    <p><strong>使用说明：</strong></p>
                    <ul>
                        <li>id: 验证码唯一标识</li>
                        <li>captchaType: 验证码类型</li>
                        <li>trackList: 滑动轨迹数据（实际使用中由前端JS捕获）</li>
                        <li>startSlidingTime/entSlidingTime: 滑动开始和结束时间</li>
                    </ul>
                </body>
                </html>
            `);
        }
        
        // 获取资源信息
        async function getResourceInfo() {
            const resultDiv = document.getElementById('resourceResult');
            
            try {
                addLog('发起获取资源信息请求');
                resultDiv.innerHTML = '<div class="info">正在获取资源信息...</div>';
                
                const response = await fetch('/slide-captcha/resources');
                const data = await response.json();
                
                addLog(`资源信息响应: ${JSON.stringify(data, null, 2)}`);
                
                if (data.code === 200) {
                    const resourceData = data.data;
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>✅ 资源信息获取成功！</strong><br>
                            资源总数：${resourceData.resourceCount}<br>
                            资源类型：${resourceData.resources.join(', ')}<br>
                            获取时间：${new Date().toLocaleString()}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <strong>❌ 获取资源信息失败</strong><br>
                            错误信息：${data.msg || '未知错误'}
                        </div>
                    `;
                }
            } catch (error) {
                addLog(`获取资源信息异常: ${error.message}`, 'error');
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>❌ 网络请求失败</strong><br>
                        错误信息：${error.message}
                    </div>
                `;
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('页面加载完成，tianai-captcha 演示页面已就绪');
        });
    </script>
</body>
</html> 