package com.oto;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.context.annotation.ComponentScan;

/**
 * 启动程序
 *
 * <AUTHOR>
 */

@SpringBootApplication
@ComponentScan(basePackages = {
    "org.dromara",
    "com.oto"
})
@MapperScan(basePackages = {
    "org.dromara.**.mapper",
    "com.oto.**.mapper"
})
public class OtoAdminApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(OtoAdminApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  OTO-Life启动成功   ლ(´ڡ`ლ)ﾞ");
    }

}
