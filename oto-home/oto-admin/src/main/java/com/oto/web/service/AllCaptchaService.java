package com.oto.web.service;

import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import jakarta.servlet.http.HttpServletRequest;
import com.oto.web.domain.dto.CaptchaDTO;
import com.oto.web.domain.vo.CaptchaVo;

/**
 * 统一验证码服务接口
 *
 * <AUTHOR>
 * @createTime 2025/7/3 15:54
 * @description
 */
public interface AllCaptchaService {

    /**
     * 生成并缓存短信验证码（带AJ滑块校验）
     * @param captchaDTO 包含手机号、AJ滑块验证码等信息
     * @return 错误信息，成功返回null
     */
    String generateAndCacheSmsCode(CaptchaDTO captchaDTO);

    /**
     * 校验短信验证码
     * @param phonenumber 手机号
     * @param code 验证码
     * @return 校验结果，true为通过，false为失败
     */
    boolean verifySmsCode(String phonenumber, String code);

    /**
     * 生成并缓存邮箱验证码
     * @param email 邮箱地址
     */
    void generateAndCacheEmailCode(String email);

    /**
     * 校验邮箱验证码
     * @param email 邮箱地址
     * @param code 验证码
     * @return 校验结果，true为通过，false为失败
     */
    boolean verifyEmailCode(String email, String code);

    /**
     * 生成AJ滑块验证码
     * @param data 滑块验证码请求参数
     * @param request HttpServletRequest
     * @return 滑块验证码图片等信息
     */
    ResponseModel getAjCaptcha(CaptchaVO data, HttpServletRequest request);

    /**
     * 校验AJ滑块验证码
     * @param data 滑块验证码校验参数
     * @param request HttpServletRequest
     * @return 校验结果
     */
    ResponseModel checkAjCaptcha(CaptchaVO data, HttpServletRequest request);

    /**
     * 二次校验AJ滑块验证码（简化版）
     * @param data 滑块验证码校验参数
     * @return 校验结果
     */
    ResponseModel verifyAjCaptcha(CaptchaVO data);

    /**
     * 生成图形验证码
     * @return 验证码信息（uuid, img）
     */
    CaptchaVo generateCaptchaCode();

    /**
     * 校验图形验证码
     * @param uuid 验证码uuid
     * @param code 验证码内容
     * @return 校验结果，true为通过，false为失败
     */
    boolean verifyCaptchaCode(String uuid, String code);

    /**
     * 获取远程ID（IP+UserAgent）
     * @param request HttpServletRequest
     * @return 远程ID字符串
     */
    String getRemoteId(HttpServletRequest request);

    /**
     * 生成并缓存简单的短信验证码（不含AJ滑块校验）
     * @param phonenumber 手机号
     */
    void generateAndCacheSimpleSmsCode(String phonenumber);
}
