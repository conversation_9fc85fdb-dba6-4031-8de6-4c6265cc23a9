package com.oto.web.config;

import com.anji.captcha.model.common.CaptchaTypeEnum;
import com.anji.captcha.model.common.Const;
import com.anji.captcha.service.CaptchaService;
import com.anji.captcha.service.impl.CaptchaServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.util.FileCopyUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * 验证码配置
 # 滑动验证，底图路径，不配置将使用默认图片
 # 支持全路径
 # 支持项目路径,以classpath:开头,取resource目录下路径,例：classpath:images/jigsaw
 aj.captcha.jigsaw=classpath:images/jigsaw
 #滑动验证，底图路径，不配置将使用默认图片
 ##支持全路径
 # 支持项目路径,以classpath:开头,取resource目录下路径,例：classpath:images/pic-click
 aj.captcha.pic-click=classpath:images/pic-click

 # 对于分布式部署的应用，我们建议应用自己实现CaptchaCacheService，比如用Redis或者memcache，
 # 参考CaptchaCacheServiceRedisImpl.java
 # 如果应用是单点的，也没有使用redis，那默认使用内存。
 # 内存缓存只适合单节点部署的应用，否则验证码生产与验证在节点之间信息不同步，导致失败。
 # ！！！ 注意啦，如果应用有使用spring-boot-starter-data-redis，
 # 请打开CaptchaCacheServiceRedisImpl.java注释。
 # redis ----->  SPI： 在resources目录新建META-INF.services文件夹(两层)，参考当前服务resources。
 # 缓存local/redis...
 aj.captcha.cache-type=local
 # local缓存的阈值,达到这个值，清除缓存
 #aj.captcha.cache-number=1000
 # local定时清除过期缓存(单位秒),设置为0代表不执行
 #aj.captcha.timing-clear=180
 #spring.redis.host=************
 #spring.redis.port=6379
 #spring.redis.password=
 #spring.redis.database=2
 #spring.redis.timeout=6000

 # 验证码类型default两种都实例化。
 aj.captcha.type=default
 # 汉字统一使用Unicode,保证程序通过@value读取到是中文，可通过这个在线转换;yml格式不需要转换
 # https://tool.chinaz.com/tools/unicode.aspx 中文转Unicode
 # 右下角水印文字(我的水印)
 aj.captcha.water-mark=\u6211\u7684\u6c34\u5370
 # 右下角水印字体(不配置时，默认使用文泉驿正黑)
 # 由于宋体等涉及到版权，我们jar中内置了开源字体【文泉驿正黑】
 # 方式一：直接配置OS层的现有的字体名称，比如：宋体
 # 方式二：自定义特定字体，请将字体放到工程resources下fonts文件夹，支持ttf\ttc\otf字体
 # aj.captcha.water-font=WenQuanZhengHei.ttf
 # 点选文字验证码的文字字体(文泉驿正黑)
 # aj.captcha.font-type=WenQuanZhengHei.ttf
 # 校验滑动拼图允许误差偏移量(默认5像素)
 aj.captcha.slip-offset=5
 # aes加密坐标开启或者禁用(true|false)
 aj.captcha.aes-status=true
 # 滑动干扰项(0/1/2)
 aj.captcha.interference-options=2

 #点选字体样式 默认Font.BOLD
 aj.captcha.font-style=1
 #点选字体字体大小
 aj.captcha.font-size=25
 #点选文字个数,存在问题，暂不支持修改
 #aj.captcha.click-word-count=4

 aj.captcha.history-data-clear-enable=false

 # 接口请求次数一分钟限制是否开启 true|false
 aj.captcha.req-frequency-limit-enable=false
 # 验证失败5次，get接口锁定
 aj.captcha.req-get-lock-limit=5
 # 验证失败后，锁定时间间隔,s
 aj.captcha.req-get-lock-seconds=360
 # get接口一分钟内请求数限制
 aj.captcha.req-get-minute-limit=30
 # check接口一分钟内请求数限制
 aj.captcha.req-check-minute-limit=60
 # verify接口一分钟内请求数限制
 aj.captcha.req-verify-minute-limit=60
 */
@Slf4j
@Configuration
public class AjCaptchaConfig {

    @Bean
    public CaptchaService captchaService() {
        Properties config = new Properties();
        try {
            // 初始化底图
            initializeImages();

            // 设定验证码类型
            config.put(Const.CAPTCHA_TYPE, CaptchaTypeEnum.DEFAULT.getCodeValue());
//            otoconfig.put(Const.CAPTCHA_AES_STATUS, "false");
            config.put(Const.CAPTCHA_CACHETYPE, "redis");
         return   CaptchaServiceFactory.getInstance(config);
//            return  new CaptchaCacheServiceRedisImpl(redisTemplate);;
        } catch (Exception e) {
            log.error("初始化验证码服务异常", e);
            return null;
        }
    }



    /**
     * 初始化验证码底图，确保底图正确加载
     */
    private void initializeImages() {
        try {
            ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resolver.getResources("classpath:defaultImages/jigsaw/*/*.png");
//            Resource[] resources = resolver.getResources("classpath:defaultImages/jigsaw/");
            log.info("找到滑块验证码底图: {} 张", resources.length);

            // 初始化滑块图片
            Map<String, byte[]> jigsawImages = new HashMap<>();
            for (Resource resource : resources) {
                String filename = resource.getFilename();
                byte[] bytes = FileCopyUtils.copyToByteArray(resource.getInputStream());
                jigsawImages.put(filename, bytes);
                log.info("成功加载滑块底图: {}", filename);
            }

            // 初始化小图片
            resources = resolver.getResources("classpath:defaultImages/jigsaw/slidingBlock/*.png");
            log.info("找到滑块: {} 张", resources.length);
            Map<String, byte[]> slidingBlockImages = new HashMap<>();
            for (Resource resource : resources) {
                String filename = resource.getFilename();
                byte[] bytes = FileCopyUtils.copyToByteArray(resource.getInputStream());
                slidingBlockImages.put(filename, bytes);
                log.info("成功加载滑块: {}", filename);
            }
        } catch (Exception e) {
            log.error("初始化验证码图片失败", e);
        }
    }


}
