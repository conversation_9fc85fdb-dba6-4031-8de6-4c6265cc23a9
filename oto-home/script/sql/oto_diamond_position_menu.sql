-- =============================================
-- 金刚位配置管理菜单权限配置
-- 版本: 1.0
-- 创建时间: 2025-01-23
-- 说明: 为金刚位配置管理功能添加系统菜单和权限
-- =============================================

-- 菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
VALUES('金刚位配置', '2000', '1', 'diamond-position', 'front/config/diamond-position/index', 1, 0, 'C', '0', '0', 'front:diamond:list', 'grid', 103, 1, sysdate(), null, null, '金刚位配置菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
VALUES('金刚位配置查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'front:diamond:query',        '#', 103, 1, sysdate(), null, null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
VALUES('金刚位配置新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'front:diamond:add',          '#', 103, 1, sysdate(), null, null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
VALUES('金刚位配置修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'front:diamond:edit',         '#', 103, 1, sysdate(), null, null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
VALUES('金刚位配置删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'front:diamond:remove',       '#', 103, 1, sysdate(), null, null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
VALUES('金刚位配置导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'front:diamond:export',       '#', 103, 1, sysdate(), null, null, '');

-- 字典类型
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_dept, create_by, create_time, update_by, update_time, remark)
VALUES ('图标类型', 'diamond_icon_type', '0', 103, 1, sysdate(), null, null, '金刚位图标类型列表');

-- 字典数据
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_dept, create_by, create_time, update_by, update_time, remark)
VALUES(1,  'URL链接',     'url',     'diamond_icon_type', '',   'default', 'Y', '0', 103, 1, sysdate(), null, null, '图标为URL链接地址');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_dept, create_by, create_time, update_by, update_time, remark)
VALUES(2,  'SVG代码',     'svg',     'diamond_icon_type', '',   'primary', 'N', '0', 103, 1, sysdate(), null, null, '图标为SVG矢量图代码');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_dept, create_by, create_time, update_by, update_time, remark)
VALUES(3,  'Base64编码',  'base64',  'diamond_icon_type', '',   'info', 'N', '0', 103, 1, sysdate(), null, null, '图标为Base64编码图片');
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_dept, create_by, create_time, update_by, update_time, remark)
VALUES(4,  '表情符号',     'emoji',   'diamond_icon_type', '',   'success', 'N', '0', 103, 1, sysdate(), null, null, '图标为Unicode表情符号');
