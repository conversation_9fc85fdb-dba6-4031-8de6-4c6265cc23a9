-- =============================================
-- 金刚位配置管理表 - oto_diamond_position
-- 版本: 1.0
-- 创建时间: 2025-01-23
-- 说明: 金刚位配置管理的简化表结构
-- =============================================

-- 创建金刚位配置表
CREATE TABLE IF NOT EXISTS `oto_diamond_position` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` varchar(100) NOT NULL COMMENT '金刚位名称',
    `icon` text DEFAULT NULL COMMENT '图标内容（URL、SVG代码、Base64等）',
    `icon_type` varchar(20) DEFAULT 'url' COMMENT '图标类型：url-链接地址，svg-SVG代码，base64-Base64编码，emoji-表情符号',
    `url` varchar(500) DEFAULT NULL COMMENT '跳转链接',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序序号',
    `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `description` varchar(500) DEFAULT NULL COMMENT '描述',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
    `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
    `create_dept` bigint COMMENT '创建部门',
    `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    PRIMARY KEY (`id`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_status` (`status`),
    KEY `idx_icon_type` (`icon_type`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='金刚位配置表';

-- 插入示例数据
INSERT INTO `oto_diamond_position` (`name`, `icon`, `icon_type`, `url`, `sort_order`, `status`, `description`) VALUES
('扫一扫', 'https://example.com/icons/scan.png', 'url', '/scan', 1, 1, '扫码功能'),
('付款码', '💰', 'emoji', '/payment', 2, 1, '付款码功能'),
('充值', '<svg viewBox="0 0 24 24"><path d="M12 2L2 7v10c0 5.55 3.84 10 9 10s9-4.45 9-10V7l-10-5z"/></svg>', 'svg', '/recharge', 3, 1, '账户充值'),
('转账', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64', '/transfer', 4, 0, '转账功能'),
('理财', 'https://example.com/icons/finance.png', 'url', '/finance', 5, 1, '理财产品'),
('信用卡', '💳', 'emoji', '/credit-card', 6, 1, '信用卡服务'),
('保洁服务', 'https://example.com/icons/cleaning.png', 'url', '/pages/service/cleaning', 7, 1, '家庭保洁服务'),
('维修服务', '🔧', 'emoji', '/pages/service/repair', 8, 1, '家电维修服务'),
('反馈建议', '💬', 'emoji', '/feedback', 9, 1, '提交反馈'),
('邀请好友', 'https://example.com/icons/invite.png', 'url', '/invite', 10, 1, '邀请好友获得奖励'),
('签到', '📅', 'emoji', '/checkin', 11, 1, '每日签到'),
('活动中心', '🎉', 'emoji', '/activities', 12, 1, '参与活动');
