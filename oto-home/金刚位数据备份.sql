-- 金刚位数据备份 (备份时间: 2025-01-21)
-- 用于API测试后的数据恢复

-- 清空表数据
TRUNCATE TABLE oto_diamond_position;

-- 恢复原始数据
INSERT INTO oto_diamond_position (id, name, icon, icon_type, url, sort_order, status, description, created_time, updated_time, created_by, updated_by, tenant_id, create_dept, del_flag) VALUES
(1, '扫一扫', 'https://example.com/icons/scan.png', 'url', '/scan', 1, 1, '扫码功能', '2025-08-23 14:16:50', '2025-08-23 14:16:50', NULL, NULL, '000000', NULL, 0),
(2, '付款码', '💰', 'emoji', '/payment', 10, 1, '付款码功能', '2025-08-23 14:16:50', '2025-08-23 14:23:41', NULL, NULL, '000000', NULL, 0),
(3, '充值', '<svg viewBox="0 0 24 24"><path d="M12 2L2 7v10c0 5.55 3.84 10 9 10s9-4.45 9-10V7l-10-5z"/></svg>', 'svg', '/recharge', 3, 1, '账户充值', '2025-08-23 14:16:50', '2025-08-23 14:16:50', NULL, NULL, '000000', NULL, 0),
(4, '转账', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64', '/transfer', 4, 0, '转账功能', '2025-08-23 14:16:50', '2025-08-23 14:16:50', NULL, NULL, '000000', NULL, 0),
(5, '理财', 'https://example.com/icons/finance.png', 'url', '/finance', 5, 1, '理财产品', '2025-08-23 14:16:50', '2025-08-23 14:16:50', NULL, NULL, '000000', NULL, 0),
(6, '信用卡', '💳', 'emoji', '/credit-card', 6, 1, '信用卡服务', '2025-08-23 14:16:50', '2025-08-23 14:16:50', NULL, NULL, '000000', NULL, 0),
(7, '保洁服务', 'https://example.com/icons/cleaning.png', 'url', '/pages/service/cleaning', 7, 1, '家庭保洁服务', '2025-08-23 14:16:50', '2025-08-23 14:16:50', NULL, NULL, '000000', NULL, 0),
(8, '维修服务', '🔧', 'emoji', '/pages/service/repair', 8, 0, '家电维修服务', '2025-08-23 14:16:50', '2025-08-23 14:23:13', NULL, NULL, '000000', NULL, 0),
(9, '测试功能', '🧪', 'emoji', '/test', 9, 1, '测试新增功能', '2025-08-23 14:24:03', '2025-08-23 14:24:03', NULL, NULL, '000000', NULL, 0);

-- 重置自增ID (如需要可手动执行)
-- ALTER TABLE oto_diamond_position AUTO_INCREMENT = 10;